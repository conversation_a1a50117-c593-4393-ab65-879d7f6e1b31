<?php
include plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_header.php';
include plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_breadcrumb.php';
?>
<div class="container-fluid px-0">
  <div class="row mx-0">
    <?php include plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_sidebar.php'; ?>
    <div id="dash" class="tabcontent">
      <?php
      // Display breadcrumb
      designer_breadcrumb(
        'Create Template',
        array(
          array(
            'title' => 'Templates Management',
            'link' => home_url('/designer/manage-templates')
          )
        )
      );
      ?>

      <div class="row">
        <!-- Template Information Form -->
        <div class="col-md-3">
          <div class="template-info-container">
            <h5 class="mb-4">Template Information</h5>
            <form id="template-info-form">
              <div class="mb-4">
                <label for="template-name" class="form-label required">Template Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="template-name" name="template-name" placeholder="Enter template name" required>
                <div class="error-message" id="template-name-error"></div>
              </div>

              <div class="mb-4">
                <label for="industries" class="form-label required">Industries <span class="text-danger">*</span></label>
                <div class="industries-select-container">
                  <!-- Selected industries tags -->
                  <div class="selected-industries" id="selected-industries">
                    <!-- Selected tags will appear here -->
                  </div>

                  <!-- Dropdown input -->
                  <div class="industries-dropdown">
                    <input type="text"
                           class="form-control"
                           id="industries-input"
                           placeholder="Type to search industries..."
                           autocomplete="off">
                    <div class="industries-list" id="industries-list" style="display: none;">
                      <!-- Industries options will be populated here -->
                    </div>
                  </div>

                  <!-- Hidden input to store selected values -->
                  <input type="hidden" id="industries" name="industries" required>
                </div>
                <div class="error-message" id="industries-error"></div>
              </div>

              <div class="mb-4">
                <label for="template-description" class="form-label required">Template Description <span class="text-danger">*</span></label>
                <textarea class="form-control" id="template-description" name="template-description" rows="4" placeholder="Describe your template..." required></textarea>
                <div class="error-message" id="template-description-error"></div>
              </div>

              <!-- <div class="mb-4">
                <label for="note" class="form-label">Note</label>
                <textarea class="form-control" id="note" name="note" rows="3" placeholder="Additional notes (optional)"></textarea>
              </div> -->
            </form>
          </div>
        </div>

        <!-- Choose Template Section -->
        <div class="col-md-9">
          <div class="choose-template-container">
            <h5 class="mb-4">Choose template</h5>

            <!-- Search and Filter Section -->
            <div class="search-filter-section mb-4">
              <div class="search-container">
                <div class="search-input-wrapper">
                  <i class="fa fa-search search-icon"></i>
                  <input type="text" class="search-input" placeholder="Search template" id="template-search">
                </div>
                <button class="search-btn" type="button" id="search-btn">Search</button>
              </div>

              <div class="filter-container">
                <span>Filter</span>

                <div class="industry-dropdown">
                  <select class="industry-select" id="industry-filter">
                    <option value=""> Select Industry</option>
                    
                  </select>
                  <i class="fa fa-chevron-down dropdown-arrow"></i>
                </div>
              </div>
            </div>
            <!-- Template Grid -->
            <div class="template-grid">
              <div class="row" id="template-list">
                <!-- Blank Template -->
                <div class="col-lg-4 col-md-6 mb-4" id="default_item">
                  <div class="template-card">
                    <div class="template-image">
                      <img src="<?php echo plugin_dir_url(__FILE__); ?>img/img-blank.png" alt="Blank Template" class="img-fluid">
                      <div class="template-overlay">
                        <div class="template-actions">
                          <button class="btn btn-outline-light btn-sm me-2" data-template-id="blank">
                            <i class="fa fa-eye me-1"></i>Preview
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="template-content">
                      <p class="template-category mb-1">&nbsp;</p>
                      <h6 class="template-title">Blank Template with Elementor</h6>
                      <div class="template-footer">
                        <button class="btn btn-outline-secondary btn-sm choose-template-btn" data-type="1" data-template-id="0" data-template-name="Blank Template with Elementor">
                          Choose
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <?php for($i = 0; $i < 10; $i++){   ?>
                <!-- Template 1 -->
                <!-- <div class="col-lg-4 col-md-6 mb-4 item_template_kit">
                  <div class="template-card">
                    <div class="template-image">
                      <img src="<?php echo plugin_dir_url(__FILE__); ?>img/image.png" alt="Template Preview" class="img-fluid">
                      <div class="favorite-icon">
                        <button class="btn btn-outline-light btn-sm favorite-btn" data-template-id="template1">
                          <i class="fa fa-heart-o"></i>
                        </button>
                      </div>
                    </div>
                    <div class="template-content">
                      <p class="template-category mb-1">Online Store</p>
                      <h6 class="template-title">The name of template</h6>
                      <div class="template-footer">
                        <button class="btn btn-outline-secondary btn-sm me-2" data-template-id="template1">
                          <i class="fa fa-eye me-1"></i>Preview
                        </button>
                        <button class="btn btn-outline-secondary btn-sm choose-template-btn" data-template-id="template1" data-template-name="The name of template">
                          Choose
                        </button>
                      </div>
                    </div>
                  </div>
                </div> -->
                <?php } ?>
            </div>
              </div>

              <!-- Next Button -->
              <div class="d-flex justify-content-end mt-4">
                <!-- <button class="btn btn-outline-info me-2" id="test-bearer-btn">
                  <i class="fa fa-key me-2"></i>Test Bearer Token
                </button> -->
                <button class="btn btn-success" id="next-btn" disabled>
                  <i class="fa fa-arrow-right me-2"></i>Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
    function fetchTemplates(keyword = "", industry_selected = "") {
      // Show loading indicator
      showTemplatesLoading();

      // Nếu có từ khóa thì tạo filters là mảng, không thì để null
      const filters = ["status_id:=(2)", "is_kit:=(1)"];
      const search = keyword ? keyword : "";

      // Add industry filter only if industry_selected is not 0
      if (industry_selected != 'all') {
          filters.push(`industries.id:[](${industry_selected})`);
      }

      const query = `
        query Template_list($filters: [String!], $search: String!) {
          template_list(body: {filters: $filters, search: $search }) {
                totalCount
                totalPages
                currentPage
                data {
                    id
                    name
                    desc
                    image_id
                    image {
                      id
                      file_name
                      file_url
                    }
                    industries {
                      id
                      name
                    }
                    info
                    status_id
                }
          }
        }`;

      $.ajax({
        url: iptHomeAjax.ajax_url,
        type: 'POST',
        dataType: 'json',
        data: {
              action: 'ipt_home_graphql',
              query: query,
              variables: JSON.stringify({ filters: filters, search: search })
        },
        success: function(response) {
              if(response.data && response.data.template_list) {
                  renderTemplates(response.data.template_list.data, industry_selected);
              } else {
                  // Show no templates found message
                  showNoTemplatesMessage();
              }
        },
        error: function(xhr, status, error) {
              console.error('Error fetching templates:', error);
              showTemplatesError();
        },
        complete: function() {
              // Hide loading indicator when request completes
              hideTemplatesLoading();
        }
      });
  }

  // Helper functions for loading states
  function showTemplatesLoading() {
      // Remove existing dynamic templates
      $('.item_template_kit').remove();

      // Add loading indicator after the default item
      const loadingHtml = `
          <div class="col-12 text-center py-4 item_template_kit" id="templates-loading">
              <div class="d-flex flex-column align-items-center">
                  <i class="fa fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                  <p class="text-muted mb-0">Loading templates...</p>
              </div>
          </div>
      `;
      $('#template-list').append(loadingHtml);
  }

  function hideTemplatesLoading() {
      $('#templates-loading').remove();
  }

  function showNoTemplatesMessage() {
      $('.item_template_kit').remove();
      const noTemplatesHtml = `
          <div class="col-12 text-center py-4 item_template_kit">
              <div class="d-flex flex-column align-items-center">
                  <i class="fa fa-search fa-2x text-muted mb-3"></i>
                  <p class="text-muted mb-0">No templates found matching your criteria.</p>
              </div>
          </div>
      `;
      $('#template-list').append(noTemplatesHtml);
  }

  function showTemplatesError() {
      $('.item_template_kit').remove();
      const errorHtml = `
          <div class="col-12 text-center py-4 item_template_kit">
              <div class="d-flex flex-column align-items-center">
                  <i class="fa fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                  <p class="text-danger mb-0">Error loading templates. Please try again.</p>
              </div>
          </div>
      `;
      $('#template-list').append(errorHtml);
  }

  // Render templates
  function renderTemplates(list, industry_selected = "") {
      // Clear only dynamic template items, keep default item
      $('.item_template_kit').remove();

      let html = '';
      list.forEach(function(item) {
        // let industry_name = item.industries[0] ? item.industries[0].name : "";
        // if(industry_selected.length > 0) {
        //       if(industry_selected !== industry_name) {
        //           return;
        //       }
        // }
        let domain = item.info ? item.info.domain : "";

        html += `
              <div class="col-lg-4 col-md-6 mb-4 item_template_kit">
                <div class="template-card">
                  <div class="template-image">
                    <img src="${item.image ? item.image.file_url : 'https://cdn.builder.io/api/v1/image/assets/TEMP/11ce7b98d60f888e40ee582f5d48055fe0e8fdca'}" class="img-fluid">
                    <!-- <div class="favorite-icon">
                      <button class="btn btn-outline-light btn-sm favorite-btn" data-type="2" data-template-id="${item.id}">
                        <i class="fa fa-heart-o"></i>
                      </button>
                    </div> -->
                  </div>
                  <div class="template-content">
                    <p class="template-category mb-1">${item.industries[0] ? item.industries[0].name : ''}</p>
                    <h6 class="template-title"> ${item.name} </h6>
                    <div class="template-footer">
                      <button class="btn btn-outline-secondary btn-sm me-2" onclick="window.open('${domain}', '_blank');" data-template-id="${item.id}">
                        <i class="fa fa-eye me-1"></i>Preview
                      </button>
                      <button class="btn btn-outline-secondary btn-sm choose-template-btn" data-template-id="${item.id}" data-template-name="${item.name}">
                        Choose
                      </button>
                    </div>
                  </div>
                </div>
              </div>
        `;
      });

      // Append new templates after the default item
      $('#template-list').append(html);
  }

  jQuery(document).ready(function($) {
      let debounceTimeout;
      

      // Get all templates
      fetchTemplates("");

      $('#search-form').on('submit', function(e) {
          e.preventDefault();
          const keyword = $('#template-search').val();
          fetchTemplates(keyword);
      });

      
      
      /* Industry list */
      const industry_filters = ["status_id:=(2)"];

      const queryIndustry = `
          query Industry_list($filters: [String!]) {
            industry_list(body: { filters: $filters }) {
                  id
                  name
                  status_id
            }
          }
      `;

      $.ajax({
            url: iptHomeAjax.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'ipt_home_graphql',
                query: queryIndustry,
                variables: JSON.stringify({ filters: industry_filters })

            },
            success: function(response) {
                if(response.data && response.data.industry_list) {
                  renderIndustries(response.data.industry_list);
                  // Also populate the tag-style multi-select
                  populateIndustriesList(response.data.industry_list);
                } else {
                  // No data
                }
            },
            error: function(xhr, status, error) {
                console.error(error);
            }
      });

      // Hàm render ra HTML for both form select and filter select
      function renderIndustries(list) {
            let html = '';

            list.forEach(function(item) {
                const industryId = item.id;
                const industryName = item.name;

                html += `
                      <option value="${industryId}">${industryName}</option>
                `;
            });

            // Populate industries list for tag-style multi-select
            console.log('=== CALLING populateIndustriesList ===');
            console.log('Function exists:', typeof window.populateIndustriesList);
            if (typeof window.populateIndustriesList === 'function') {
              window.populateIndustriesList(list);
            } else {
              console.error('populateIndustriesList function not found! Defining it now...');
              // Define the function inline as fallback
              window.populateIndustriesList = function(industries) {
                console.log('=== INLINE POPULATING INDUSTRIES LIST ===');
                console.log('Industries received:', industries);
                window.allIndustries = industries;
                if (typeof window.renderIndustriesList === 'function') {
                  window.renderIndustriesList(industries);
                }
              };
              window.populateIndustriesList(list);
            }

            console.log('=== INDUSTRIES LOADED ===');
            console.log('Industries:', list);

            // Populate filter select (#industry-filter)
            $('#industry-filter').html('<option value="all">All Industries</option>' + html);

            // Keep existing dropdown functionality if needed
            let html_dropdown = '';
            list.forEach(function(item) {
                html_dropdown += `
                  <div data-industry-id="${item.id}" class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                        ${item.name}
                  </div>
                `;
            });
            $('#industryDropdown').html(html_dropdown);
      }

        // Xử lý sự kiện khi thay đổi giá trị dropdown cho filter
      $('#industry-filter').on('change', function() {
          const selectedOption = $(this).find('option:selected').val();
          const keyword = $('#template-search').val();
          fetchTemplates(keyword, selectedOption);
      });

      // Keep existing industry dropdown handler if needed
      // $('#industry').on('change', function() {
      //     const selectedOption = $(this).find('option:selected');
      //     const industryName = selectedOption.text();
      //     const keyword = $('#search-template').val();
      //     fetchTemplates(keyword, industryName);
      // });
      
      // Xử lý sự kiện khi click vào item trong dropdown
      $(document).on('click', '#industryDropdown div', function() {
          const industryName = $(this).data('industry-name');
          const industryId = $(this).data('industry-id');
           const keyword = $('#template-search').val();
          
          // Cập nhật giá trị cho select
          $('#industry').val(industryId);
          
          // Ẩn dropdown
          $('#industryDropdown').addClass('hidden');
          
          // Gọi lại fetchTemplates với industry đã chọn
          fetchTemplates(keyword, industryName);
      });
    
      
    });
</script> 
<style>
  /* Template Information Container */
  .template-info-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border: 1px solid #e9ecef;
    height: fit-content;
  }

  .choose-template-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border: 1px solid #e9ecef;
  }

  /* Form Styling - Match info.php */
  .form-control {
    background-color: #f0f5ff;
    border: 1px solid #e9ecef;
    padding: 12px 15px;
    height: auto;
    border-radius: 6px;
  }

  .form-control:focus {
    background-color: #fff;
    border-color: #4DCDB4;
    box-shadow: 0 0 0 0.2rem rgba(77, 205, 180, 0.25);
  }

  .form-control.is-invalid {
    border: 1px solid #dc3545;
    background-color: #fff8f8;
  }

  .form-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
  }

  .form-label.required {
    position: relative;
  }

  .text-danger {
    color: #dc3545 !important;
  }

  .error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    min-height: 18px;
  }

  /* Search and Filter Section */
  .search-filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
  }

  .search-container {
    display: flex;
    align-items: center;
    gap: 0;
    flex: 1;
    max-width: 500px;
  }

  .search-input-wrapper {
    position: relative;
    flex: 1;
    background: #f5f5f5;
    border-radius: 8px 0 0 8px;
    border: 1px solid #e0e0e0;
    border-right: none;
    display: flex;
    align-items: center;
    padding: 0 15px;
    height: 44px;
  }

  .search-icon {
    color: #999;
    margin-right: 10px;
    font-size: 16px;
  }

  .search-input {
    border: none;
    background: transparent;
    outline: none;
    flex: 1;
    font-size: 14px;
    color: #333;
    height: 100%;
  }

  .search-input::placeholder {
    color: #999;
  }

  .search-btn {
    background: #4DCDB4;
    color: white;
    border: none;
    padding: 0 24px;
    height: 44px;
    border-radius: 0 8px 8px 0;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .search-btn:hover {
    background: #3bb89f;
  }

  .filter-container {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .filter-btn {
    background: #f5f5f5;
    border: 1px solid #e0e0e0;
    color: #666;
    padding: 0 20px;
    height: 44px;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .filter-btn:hover {
    background: #e9ecef;
    border-color: #ccc;
  }

  .industry-dropdown {
    position: relative;
    display: flex;
    align-items: center;
  }

  .industry-select {
    appearance: none;
    background: #f5f5f5;
    border: 1px solid #e0e0e0;
    color: #666;
    padding: 0 40px 0 20px;
    height: 44px;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    min-width: 120px;
    transition: all 0.3s ease;
  }

  .industry-select:hover {
    background: #e9ecef;
    border-color: #ccc;
  }

  .industry-select:focus {
    outline: none;
    border-color: #4DCDB4;
    box-shadow: 0 0 0 2px rgba(77, 205, 180, 0.2);
  }

  .dropdown-arrow {
    position: absolute;
    right: 15px;
    color: #999;
    font-size: 12px;
    pointer-events: none;
  }

  /* Template Card Styling */
  .template-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #fff;
    height: 100%;
  }

  .template-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
  }

  .template-image {
    position: relative;
    height: 200px;
    overflow: hidden;
  }

  .template-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .favorite-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
  }

  .favorite-icon .favorite-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .favorite-icon .favorite-btn:hover {
    background: rgba(255, 255, 255, 1);
    color: #e54d2e;
    transform: scale(1.1);
  }

  .favorite-icon .favorite-btn.active {
    background: #e54d2e;
    color: white;
  }

  .favorite-icon .favorite-btn.active:hover {
    background: #d63384;
  }

  .template-content {
    padding: 15px;
  }

  .template-category {
    color: #6c757d;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .template-title {
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }

  .template-footer {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .template-footer .btn {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 12px;
    font-size: 13px;
    line-height: 1;
  }

  /* Button Styling - Match manage_templates.php */
  .btn-success {
    background-color: #4DCDB4;
    border-color: #4DCDB4;
    padding: 8px 16px;
  }

  .btn-success:hover {
    background-color: #3bb89f;
    border-color: #3bb89f;
  }

  .btn-outline-secondary {
    border-color: #e9ecef;
    color: #6c757d;
  }

  .btn-outline-secondary:hover {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
  }

  /* Choose Template Button States */
  .choose-template-btn {
    background-color: #fff !important;
    border-color: #e9ecef !important;
    color: #6c757d !important;
  }

  .choose-template-btn:hover {
    background-color: #f8f9fa !important;
    border-color: #e9ecef !important;
    color: #6c757d !important;
  }

  .choose-template-btn.selected {
    background-color: #4DCDB4 !important;
    border-color: #4DCDB4 !important;
    color: white !important;
  }

  .choose-template-btn.selected:hover {
    background-color: #3bb89f !important;
    border-color: #3bb89f !important;
    color: white !important;
  }

  /* Favorite Button */
  .favorite-btn.active i {
    color: #e54d2e;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .template-info-container,
    .choose-template-container {
      margin-bottom: 20px;
    }

    .search-filter-section {
      flex-direction: column;
      gap: 15px;
      align-items: stretch;
    }

    .search-container {
      max-width: 100%;
    }

    .filter-container {
      justify-content: space-between;
    }

    .filter-btn,
    .industry-select {
      flex: 1;
      max-width: 48%;
    }

    .template-footer {
      flex-direction: column;
      gap: 10px;
    }

    .template-footer .btn {
      width: 100%;
    }
  }

  @media (max-width: 480px) {
    .search-input-wrapper,
    .search-btn {
      height: 40px;
    }

    .filter-btn,
    .industry-select {
      height: 40px;
      max-width: 100%;
      margin-bottom: 10px;
    }

    .filter-container {
      flex-direction: column;
      gap: 10px;
    }
  }

  /* Image styling */
  .img-detail {
    position: relative;
    height: 200px;
    overflow: hidden;
    border-radius: 6px;
  }

  .img-detail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .img-logo {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 8px;
  }

  .logo {
    width: 32px;
    height: 32px;
    background: rgba(0,0,0,0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .logo:hover {
    background: rgba(0,0,0,0.7);
  }

  .logo-like.active {
    background: rgba(229, 77, 46, 0.8);
  }

  /* Industries Multi-Select Styles */
  .industries-select-container {
    position: relative;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    min-height: 48px;
    padding: 6px;
    background: #f0f5ff;
    cursor: text;
    transition: all 0.3s ease;
  }

  .industries-select-container:focus-within {
    background-color: #fff;
    border-color: #4DCDB4;
    box-shadow: 0 0 0 0.2rem rgba(77, 205, 180, 0.25);
  }

  .selected-industries {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 4px;
  }

  .industry-tag {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 6px;
    color: #0066cc;
  }

  .industry-tag .remove-tag {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    font-size: 16px;
    line-height: 1;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .industry-tag .remove-tag:hover {
    color: #dc3545;
  }

  .industries-dropdown {
    position: relative;
  }

  #industries-input {
    border: none;
    outline: none;
    box-shadow: none;
    padding: 6px 8px;
    font-size: 0.875rem;
    min-width: 200px;
    background: transparent;
  }

  .industries-list {
    position: absolute;
    top: 100%;
    left: -6px;
    right: -6px;
    background: white;
    border: 1px solid #ced4da;
    border-radius: 0 0 6px 6px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .industry-option {
    padding: 10px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    font-size: 0.875rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .industry-option:hover:not(.disabled) {
    background: #f8f9fa;
  }

  .industry-option.selected {
    background: #e7f3ff;
    color: #0066cc;
  }

  .industry-option.disabled {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.7;
  }

  .industry-option.disabled:hover {
    background: #f8f9fa;
  }

  .selected-indicator {
    font-size: 0.75rem;
    color: #28a745;
    font-weight: 500;
  }

  .industry-option:last-child {
    border-bottom: none;
  }

  .no-results {
    padding: 10px 12px;
    color: #6c757d;
    font-style: italic;
    text-align: center;
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  let selectedTemplate = null;

  // Form validation functions
  function showError(fieldId, message) {
    const field = document.getElementById(fieldId);
    const errorElement = document.getElementById(fieldId + '-error');

    if (field && errorElement) {
      field.classList.add('is-invalid');
      errorElement.textContent = message;
    }
  }

  function clearError(fieldId) {
    const field = document.getElementById(fieldId);
    const errorElement = document.getElementById(fieldId + '-error');

    if (field && errorElement) {
      field.classList.remove('is-invalid');
      errorElement.textContent = '';
    }
  }

  function clearAllErrors() {
    const errorElements = document.querySelectorAll('.error-message');
    const invalidFields = document.querySelectorAll('.is-invalid');

    errorElements.forEach(element => {
      element.textContent = '';
    });

    invalidFields.forEach(field => {
      field.classList.remove('is-invalid');
    });
  }

  function validateForm() {
    clearAllErrors();
    let isValid = true;

    const templateName = document.getElementById('template-name').value.trim();
    const description = document.getElementById('template-description').value.trim();

    if (!templateName) {
      showError('template-name', 'Template name is required.');
      isValid = false;
    }

    if (!window.selectedIndustries || window.selectedIndustries.length === 0) {
      showError('industries', 'Please select at least one industry.');
      isValid = false;
    }

    if (!description) {
      showError('template-description', 'Template description is required.');
      isValid = false;
    }

    return isValid;
  }

  function updateNextButton() {
    const nextBtn = document.getElementById('next-btn');
    const isFormValid = validateForm();

    if (isFormValid && selectedTemplate) {
      nextBtn.disabled = false;
      nextBtn.classList.remove('btn-secondary');
      nextBtn.classList.add('btn-success');
    } else {
      nextBtn.disabled = true;
      nextBtn.classList.remove('btn-success');
      nextBtn.classList.add('btn-secondary');
    }
  }

  // Add input event listeners
  document.querySelectorAll('#template-info-form input, #template-info-form select, #template-info-form textarea').forEach(input => {
    input.addEventListener('input', function() {
      clearError(this.id);
      setTimeout(updateNextButton, 100);
    });
  });

  // Template selection using event delegation for dynamic content
  $(document).on('click', '.choose-template-btn', function() {
    // Remove previous selection from all buttons
    $('.choose-template-btn').each(function() {
      $(this).removeClass('selected btn-success').addClass('btn-outline-secondary');
      $(this).html('Choose');
    });

    // Mark current as selected
    $(this).addClass('selected').removeClass('btn-outline-secondary').addClass('btn-success');
    $(this).html('<i class="fa fa-check me-1"></i>Selected');

    // Collect only data-template-id and data-type
    selectedTemplate = {
      templateId: $(this).data('template-id') || 0,
      type: $(this).data('type') || 1
    };

    // Log selection for debugging
    console.log('=== TEMPLATE SELECTED ===');
    console.log('data-template-id:', selectedTemplate.templateId);
    console.log('data-type:', selectedTemplate.type);

    if (selectedTemplate.type == 1 && selectedTemplate.templateId == 0) {
      console.log('Template Type: Blank Template');
    } else if (selectedTemplate.type == 1 && selectedTemplate.templateId > 0) {
      console.log('Template Type: API Template');
    } else {
      console.log('Template Type: Other');
    }

    updateNextButton();
  });

  // Favorite functionality using event delegation
  $(document).on('click', '.favorite-btn', function(e) {
    e.stopPropagation(); // Prevent card selection
    $(this).toggleClass('active');
    const icon = $(this).find('i');
    if ($(this).hasClass('active')) {
      icon.removeClass('fa-heart-o').addClass('fa-heart');
    } else {
      icon.removeClass('fa-heart').addClass('fa-heart-o');
    }
  });

  // Search functionality using event delegation
  $(document).on('click', '#search-btn', function() {
    const searchTerm = $('#template-search').val().toLowerCase();
    const selectedIndustry = $("#industry-filter").find('option:selected').val();
    console.log('=== SEARCHING ===');
    console.log('Search term:', searchTerm);
    console.log('Selected industry:', selectedIndustry);
    fetchTemplates(searchTerm, selectedIndustry);
    // $('.template-card').each(function() {
    //   const title = $(this).find('.template-title').text().toLowerCase();
    //   const category = $(this).find('.template-category').text().toLowerCase() || '';

    //   if (title.includes(searchTerm) || category.includes(searchTerm)) {
    //     $(this).closest('.col-lg-4').show();
    //   } else {
    //     $(this).closest('.col-lg-4').hide();
    //   }
    // });
  });

  // // Filter functionality
  // document.getElementById('filter-template').addEventListener('change', function() {
  //   const filterValue = this.value;
  //   const templateCards = document.querySelectorAll('.template-card');

  //   templateCards.forEach(card => {
  //     if (filterValue === 'all') {
  //       card.closest('.col-lg-4').style.display = 'block';
  //     } else {
  //       // Add filter logic based on your template data
  //       card.closest('.col-lg-4').style.display = 'block';
  //     }
  //   });
  // });

  // Function to generate unique subdomain
  function generateUniqueSubdomain(templateName) {
    // Clean the template name for subdomain use
    let cleanName = templateName
      .toLowerCase()                    // Convert to lowercase
      .replace(/[^a-z0-9\s-]/g, '')    // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-')            // Replace spaces with hyphens
      .replace(/-+/g, '-')             // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, '');          // Remove leading/trailing hyphens

    // Ensure minimum length
    if (cleanName.length < 3) {
      cleanName = 'template-' + cleanName;
    }

    // Limit length to 20 characters for subdomain
    if (cleanName.length > 20) {
      cleanName = cleanName.substring(0, 20);
    }

    // Add timestamp to ensure uniqueness
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
    const uniqueName = cleanName + '-' + timestamp;

    // Generate final subdomain
    const subdomain = uniqueName + '.weaveform.com';

    console.log('=== SUBDOMAIN GENERATION ===');
    console.log('Original template name:', templateName);
    console.log('Cleaned name:', cleanName);
    console.log('Unique name with timestamp:', uniqueName);
    console.log('Final subdomain:', subdomain);

    return subdomain;
  }

  // Function to collect all form and template data
  function collectFormData() {
    // Get all form inputs, selects, and textareas
    const formElements = $('#template-info-form').find('input, select, textarea');
    const formData = {};

    console.log('=== COLLECT FORM DATA DEBUG ===');
    console.log('Found form elements:', formElements.length);

    // Collect all form field data
    formElements.each(function() {
      const element = $(this);
      const name = element.attr('name') || element.attr('id');
      const type = element.attr('type');
      let value = '';

      if (type === 'checkbox' || type === 'radio') {
        value = element.is(':checked') ? element.val() : '';
      } else if (element.is('select[multiple]')) {
        // Handle multiple select elements
        value = element.val() || []; // jQuery returns array for multiple select
      } else {
        value = element.val() || '';
      }

      if (name) {
        formData[name] = value;
        if (name === 'industries') {
          console.log('=== INDUSTRIES FIELD FOUND ===');
          console.log('Element:', element[0]);
          console.log('Name:', name);
          console.log('Type:', type);
          console.log('Value:', value);
        }
      }
    });

    // Add selected template data (only data-template-id and data-type)
    const templateSelection = {
      templateId: selectedTemplate ? selectedTemplate.templateId : null,
      type: selectedTemplate ? selectedTemplate.type : null
    };

    return {
      formData: formData,
      templateSelection: templateSelection,
      // Helper flags for template type
      isBlankTemplate: templateSelection.type == 1 && templateSelection.templateId == 0,
      isApiTemplate: templateSelection.type == 1 && templateSelection.templateId > 0
    };
  }

  // Function to create template via GraphQL API
  function createTemplate(collectedData) {
    console.log('=== CREATING TEMPLATE ===');

    // Get designer_id from user meta
    <?php
      $current_user_id = get_current_user_id();
      $designer_id = get_user_meta($current_user_id, 'designer_id', true);
      // Fallback to 1 if no designer_id found
      if (empty($designer_id)) {
          $designer_id = 1;
      } else {
          // Convert to integer for GraphQL
          $designer_id = intval($designer_id);
      }
    ?>
    const designerId = <?php echo json_encode($designer_id); ?>;

    // Get selected industry IDs from the tag-style multi-select
    const industriesValue = collectedData.formData.industries || '';
    let industryIds = [];

    console.log('=== RAW INDUSTRIES VALUE ===');
    console.log('Industries value from form:', industriesValue);
    console.log('Type:', typeof industriesValue);

    if (industriesValue) {
      // Parse comma-separated values from hidden input
      const rawIds = industriesValue.split(',');
      console.log('Split IDs:', rawIds);

      industryIds = rawIds
        .map(id => {
          const trimmed = id.trim();
          const parsed = parseInt(trimmed);
          console.log(`Converting "${trimmed}" to ${parsed}`);
          return parsed;
        })
        .filter(id => {
          const isValid = !isNaN(id) && id > 0;
          console.log(`ID ${id} is valid:`, isValid);
          return isValid;
        });
    }

    console.log('=== INDUSTRY PROCESSING ===');
    console.log('Selected industries raw:', industriesValue);
    console.log('Parsed industry IDs:', industryIds);
    console.log('Selected industries objects:', window.selectedIndustries);
    console.log('All available industries:', window.allIndustries);

    // Generate unique subdomain based on template name
    const templateName = collectedData.formData['template-name'] || 'Untitled Template';
    const uniqueDomain = generateUniqueSubdomain(templateName);

    // Prepare infoData - exclude template_id if it's 0 (blank template)
    const infoData = {
      'domain': uniqueDomain
    };

    // Only add template_id if it's not 0 (not blank template)
    if (collectedData.templateSelection.templateId && collectedData.templateSelection.templateId !== 0) {
      infoData.template_id = collectedData.templateSelection.templateId;
      console.log('Including template_id in API call:', collectedData.templateSelection.templateId);
    } else {
      console.log('Excluding template_id from API call (blank template selected)');
    }
    // Prepare GraphQL mutation
    const mutation = `
      mutation Webhooks_templates_template_create($name: String!, $desc: String!, $status_id: Int!, $designer_id: Int!, $industry_ids: [Int!]!, $is_multiple: Boolean!, $info: JSON) {
        webhooks_templates_template_create(
          body: {
            name: $name
            desc: $desc
            status_id: $status_id
            designer_id: $designer_id
            industry_ids: $industry_ids
            is_multiple: $is_multiple,
            info: $info
          }
        ) {
          id
        }
      }
    `;

    // Ensure industry_ids is always a valid array
    const validIndustryIds = Array.isArray(industryIds) && industryIds.length > 0 ? industryIds : [1]; // Default to industry ID 1 if none selected

    // Prepare variables
    const variables = {
      name: collectedData.formData['template-name'] || 'Untitled Template',
      desc: collectedData.formData['template-description'] || 'No description provided',
      status_id: 1, // Default status
      designer_id: designerId,
      industry_ids: validIndustryIds,
      is_multiple: false, // Default to false
      info: infoData  // Send as object, not JSON string
    };

    console.log('=== FINAL GRAPHQL VARIABLES ===');
    console.log('Variables to send:', variables);
    console.log('Industry IDs specifically:', variables.industry_ids);

    console.log('=== FINAL VARIABLES ===');
    console.log('Variables for GraphQL:', variables);

    console.log('GraphQL Mutation:', mutation);
    console.log('Variables:', variables);
    console.log('=== BEARER TOKEN CHECK ===');
    console.log('GRAPHQL_TOKEN defined:', '<?php echo defined('GRAPHQL_TOKEN') ? 'YES' : 'NO'; ?>');
    console.log('GRAPHQL_TOKEN value:', '<?php echo defined('GRAPHQL_TOKEN') ? GRAPHQL_TOKEN : 'UNDEFINED'; ?>');
    console.log('API will use ipt_home_graphql action which should add Bearer token automatically');

    // Show loading state
    $('#next-btn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin me-2"></i>Creating...');

    // Make API call
    $.ajax({
      url: iptHomeAjax.ajax_url,
      type: 'POST',
      dataType: 'json',
      data: {
        action: 'ipt_home_graphql',
        query: mutation,
        variables: JSON.stringify(variables)
      },
      success: function(response) {
        console.log('=== API RESPONSE ===');
        console.log(response);

        if (response.errors && response.errors.length > 0) {
          console.error('GraphQL Errors:', response.errors);
          alert('Error creating template: ' + response.errors[0].message);
        } else if (response.data && response.data.webhooks_templates_template_create) {
          const templateId = response.data.webhooks_templates_template_create.id;
          console.log('Template created successfully with ID:', templateId);

          // Call second API to check template exists
          checkTemplateExists(infoData, templateId);
        } else {
          console.error('Unexpected response format:', response);
          alert('Unexpected response from server');
        }
      },
      error: function(xhr, status, error) {
        console.error('AJAX Error:', error);
        console.error('Response:', xhr.responseText);
        alert('Failed to create template. Please try again.');
      },
      complete: function() {
        // Restore button state
        $('#next-btn').prop('disabled', false).html('<i class="fa fa-arrow-right me-2"></i>Next');
      }
    });
  }

  // Function to check if template exists after creation
  function checkTemplateExists(infoData, createdTemplateId) {
    console.log('=== CHECKING TEMPLATE EXISTS ===');
    console.log('Info data:', infoData);
    console.log('Created template ID:', createdTemplateId);

    // Prepare GraphQL query - different query based on template type
    let query, variables;

    if (infoData.template_id) {
      // Query with template_id for API templates
      query = `
        query Webhook_template_check_exits($template_id: Int!, $domain: String!) {
          webhook_template_check_exits(
            body: {
              template_id: $template_id,
              domain: $domain
            }
          )
        }
      `;

      variables = {
        template_id: infoData.template_id,
        domain: infoData.domain
      };

      console.log('Using template check query WITH template_id');
    } else {
      // Query without template_id for blank templates
      query = `
        query Webhook_template_check_exits($domain: String!) {
          webhook_template_check_exits(
            body: {
              domain: $domain
            }
          )
        }
      `;

      variables = {
        domain: infoData.domain
      };

      console.log('Using template check query WITHOUT template_id (blank template)');
    }

    console.log('Check exists query:', query);
    console.log('Check exists variables:', variables);

    // Update button to show checking state
    $('#next-btn').html('<i class="fa fa-spinner fa-spin me-2"></i>Checking...');

    // Make API call
    $.ajax({
      url: iptHomeAjax.ajax_url,
      type: 'POST',
      dataType: 'json',
      data: {
        action: 'ipt_home_graphql',
        query: query,
        variables: JSON.stringify(variables)
      },
      success: function(response) {
        console.log('=== CHECK EXISTS RESPONSE ===');
        console.log(response);

        if (response.errors && response.errors.length > 0) {
          console.error('Check exists errors:', response.errors);
          alert('Error checking template: ' + response.errors[0].message);
        } else if (response.data && response.data.webhook_template_check_exits !== undefined) {
          const exists = response.data.webhook_template_check_exits;
          console.log('Template exists check result:', exists);

          if (exists === true) {
            console.log('✅ Template verification successful!');

            // Start WordPress status polling
            startWordPressStatusPolling(infoData.domain);
          } else {
            console.log('❌ Template verification failed');
            alert('Template created but verification failed. ID: ' + createdTemplateId);
          }
        } else {
          console.error('Unexpected check exists response:', response);
          alert('Template created but verification response was unexpected. ID: ' + createdTemplateId);
        }
      },
      error: function(xhr, status, error) {
        console.error('Check exists AJAX Error:', error);
        console.error('Response:', xhr.responseText);
        alert('Template created but verification failed. ID: ' + createdTemplateId);
      },
      complete: function() {
        // Restore button state
        $('#next-btn').prop('disabled', false).html('<i class="fa fa-arrow-right me-2"></i>Next');
      }
    });
  }

  // Next button functionality with debugging
  $(document).on('click', '#next-btn', function(e) {
    e.preventDefault();
    console.log('=== NEXT BUTTON CLICKED ===');
    console.log('Button element:', this);
    console.log('Selected template:', selectedTemplate);
    console.log('Form validation result:', validateForm());

    // Always collect data for debugging
    const collectedData = collectFormData();
    console.log('=== RAW COLLECTED DATA ===');
    console.log(collectedData);

    // Check the hidden input value directly
    const hiddenInput = document.getElementById('industries');
    console.log('=== HIDDEN INPUT CHECK ===');
    console.log('Hidden input element:', hiddenInput);
    console.log('Hidden input value:', hiddenInput ? hiddenInput.value : 'NOT FOUND');
    console.log('Selected industries global:', window.selectedIndustries);

    if (validateForm() && selectedTemplate) {
      console.log('=== COMPREHENSIVE FORM DATA ===');
      console.log('All Form Fields:');
      Object.keys(collectedData.formData).forEach(key => {
        console.log(`  ${key}:`, collectedData.formData[key]);
      });

      console.log('');
      console.log('=== TEMPLATE SELECTION ===');
      console.log('data-template-id:', collectedData.templateSelection.templateId);
      console.log('data-type:', collectedData.templateSelection.type);
      console.log('Is Blank Template:', collectedData.isBlankTemplate);
      console.log('Is API Template:', collectedData.isApiTemplate);

      console.log('');
      console.log('=== COMPLETE DATA OBJECT FOR API ===');
      console.log(JSON.stringify(collectedData, null, 2));

      // Send data to API
      createTemplate(collectedData);
    } else {
      console.log('=== VALIDATION FAILED ===');
      if (!validateForm()) {
        console.log('Form validation failed - checking individual fields:');
        console.log('Template name:', $('#template-name').val());
        console.log('Industries:', $('#industries').val());
        console.log('Description:', $('#template-description').val());
      }
      if (!selectedTemplate) {
        console.log('No template selected');
      }
      alert('Please fill all required fields and select a template.');
    }
  });

  // Heart/favorite functionality for old template cards
  $('.img-logo .logo').on('click', function() {
    $(this).toggleClass('active');
  });

  // Tab functionality (if needed)
  function openForm(evt, formName) {
    var i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
    }
    tablinks = document.getElementsByClassName("tablinks");
    for (i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active", "");
    }
    document.getElementById(formName).style.display = "block";
    evt.currentTarget.className += " active";
  }

  // Initialize default tab if exists
  const defaultOpen = document.getElementById("defaultOpen");
  if (defaultOpen) {
    defaultOpen.click();
  }

  // Test button click functionality
  console.log('=== SCRIPT LOADED ===');
  console.log('Next button exists:', $('#next-btn').length > 0);
  console.log('Form exists:', $('#template-info-form').length > 0);

  // Simple test click handler
  $('#next-btn').on('click', function() {
    console.log('=== DIRECT CLICK HANDLER TRIGGERED ===');
  });

  // Test Bearer Token button
  // $('#test-bearer-btn').on('click', function() {
  //   console.log('=== TESTING BEARER TOKEN ===');

  //   // Test with Webhooks_templates_template_create mutation
  //   const testMutation = `
  //     mutation Webhooks_templates_template_create($name: String!) {
  //       webhooks_templates_template_create(
  //         body: {
  //           name: $name
  //           desc: "Test template"
  //           status_id: 1
  //           designer_id: 1
  //           industry_ids: [1]
  //           is_multiple: false
  //         }
  //       ) {
  //         id
  //       }
  //     }
  //   `;

  //   const testVariables = {
  //     name: "Bearer Token Test Template"
  //   };

  //   console.log('Test mutation:', testMutation);
  //   console.log('Test variables:', testVariables);
  //   console.log('GRAPHQL_TOKEN:', '<?php echo defined('GRAPHQL_TOKEN') ? GRAPHQL_TOKEN : 'UNDEFINED'; ?>');

  //   // Show loading state
  //   $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin me-2"></i>Testing...');

  //   $.ajax({
  //     url: iptHomeAjax.ajax_url,
  //     type: 'POST',
  //     dataType: 'json',
  //     data: {
  //       action: 'ipt_home_graphql',
  //       query: testMutation,
  //       variables: JSON.stringify(testVariables)
  //     },
  //     success: function(response) {
  //       console.log('=== BEARER TOKEN TEST RESPONSE ===');
  //       console.log(response);

  //       if (response.errors && response.errors.length > 0) {
  //         console.error('Bearer Token Test Errors:', response.errors);
  //         alert('Bearer Token Test Failed: ' + response.errors[0].message);
  //       } else if (response.data) {
  //         console.log('✅ Bearer Token Test Success!');
  //         alert('Bearer Token Test Successful! Template created with ID: ' + (response.data.webhooks_templates_template_create ? response.data.webhooks_templates_template_create.id : 'Unknown'));
  //       } else {
  //         alert('Bearer Token Test: Unexpected response format');
  //       }
  //     },
  //     error: function(xhr, status, error) {
  //       console.error('Bearer Token Test AJAX Error:', error);
  //       console.error('Response:', xhr.responseText);
  //       alert('Bearer Token Test Failed: AJAX Error - ' + error);
  //     },
  //     complete: function() {
  //       // Restore button state
  //       $('#test-bearer-btn').prop('disabled', false).html('<i class="fa fa-key me-2"></i>Test Bearer Token');
  //     }
  //   });
  // });

  // Function to start WordPress status polling
  function startWordPressStatusPolling(domain) {
    console.log('=== STARTING WORDPRESS STATUS POLLING ===');
    console.log('Domain:', domain);

    // Show loading overlay
    showLoadingOverlay();

    // Start polling every 10 seconds
    const pollInterval = setInterval(function() {
      checkWordPressStatus(domain, pollInterval);
    }, 10000);

    // Also check immediately
    checkWordPressStatus(domain, pollInterval);
  }

  // Function to check WordPress status
  function checkWordPressStatus(domain, pollInterval) {
    console.log('=== CHECKING WORDPRESS STATUS ===');
    console.log('Domain:', domain);

    // Prepare GraphQL query
    const query = `
      query Wordpress_status($domain: String!) {
        wordpress_status(domain: $domain)
      }
    `;

    // Prepare variables
    const variables = {
      domain: domain
    };

    console.log('WordPress status query:', query);
    console.log('WordPress status variables:', variables);

    // Make API call
    $.ajax({
      url: iptHomeAjax.ajax_url,
      type: 'POST',
      dataType: 'json',
      data: {
        action: 'ipt_home_graphql',
        query: query,
        variables: JSON.stringify(variables)
      },
      success: function(response) {
        console.log('=== WORDPRESS STATUS RESPONSE ===');
        console.log(response);

        if (response.errors && response.errors.length > 0) {
          console.error('WordPress status errors:', response.errors);
          // Continue polling even on error
        } else if (response.data && response.data.wordpress_status) {
          const wpStatus = response.data.wordpress_status;
          console.log('WordPress status data:', wpStatus);

          // Check if wp_password has value
          if (wpStatus.wp_password && wpStatus.wp_password.trim() !== '') {
            console.log('✅ WordPress setup complete! Password found:', wpStatus.wp_password);

            // Stop polling
            clearInterval(pollInterval);

            // Hide loading overlay
            hideLoadingOverlay();

            // Show success message
            alert('Template setup completed successfully!');

            // Redirect to manage templates with preserved parameters
            redirectWithParams('/designer/manage-templates');
          } else {
            console.log('⏳ WordPress still setting up... wp_password not ready yet');
            updateLoadingMessage('Setting up Template environment...');
          }
        } else {
          console.error('Unexpected WordPress status response:', response);
        }
      },
      error: function(xhr, status, error) {
        console.error('WordPress status AJAX Error:', error);
        console.error('Response:', xhr.responseText);
        // Continue polling even on error
      }
    });
  }

  // Function to show loading overlay
  function showLoadingOverlay() {
    const overlay = `
      <div id="wordpress-loading-overlay" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-family: Arial, sans-serif;
      ">
        <div style="text-align: center; padding: 40px;">
          <div style="margin-bottom: 20px;">
            <i class="fa fa-spinner fa-spin" style="font-size: 48px; color: #4DCDB4;"></i>
          </div>
          <h3 style="margin-bottom: 10px; color: white;">Setting Up Your Template</h3>
          <p id="loading-message" style="color: #ccc; font-size: 16px;">
            Please wait while we prepare your template environment...
          </p>
          <div style="margin-top: 20px; font-size: 14px; color: #999;">
            This may take a few minutes
          </div>
        </div>
      </div>
    `;

    $('body').append(overlay);
  }

  // Function to hide loading overlay
  function hideLoadingOverlay() {
    $('#wordpress-loading-overlay').remove();
  }

  // Function to update loading message
  function updateLoadingMessage(message) {
    $('#loading-message').text(message);
  }

  // Function to preserve URL parameters when redirecting
  function getUrlWithParams(newUrl) {
    const currentUrl = new URL(window.location.href);
    const newUrlObj = new URL(newUrl, window.location.origin);

    // Check if the new URL is a /designer/ page
    const isDesignerPage = newUrlObj.pathname.startsWith('/designer/');

    console.log('=== URL PARAMETER PRESERVATION ===');
    console.log('Original URL:', currentUrl.href);
    console.log('New URL:', newUrl);
    console.log('Is Designer Page:', isDesignerPage);

    // Only preserve parameters if NOT going to a /designer/ page
    if (!isDesignerPage) {
      // Get current URL parameters
      const currentParams = currentUrl.searchParams;

      // Preserve specific parameters
      const paramsToPreserve = ['action', 'bypass_token', 'user_id', 'template_id'];

      paramsToPreserve.forEach(param => {
        if (currentParams.has(param)) {
          newUrlObj.searchParams.set(param, currentParams.get(param));
        }
      });

      console.log('Parameters preserved for non-designer page');
    } else {
      console.log('No parameters preserved for designer page');
    }

    console.log('Final URL:', newUrlObj.href);

    return newUrlObj.href;
  }

  // Tag-style multi-select for industries
  window.selectedIndustries = [];
  window.allIndustries = [];

  window.populateIndustriesList = function(industries) {
    console.log('=== POPULATING INDUSTRIES LIST ===');
    console.log('Industries received:', industries);
    window.allIndustries = industries;
    window.renderIndustriesList(industries);
  };

  window.renderIndustriesList = function(industries) {
    console.log('=== RENDERING INDUSTRIES LIST ===');
    console.log('Industries to render:', industries);

    const industriesList = document.getElementById('industries-list');
    console.log('Industries list element:', industriesList);

    if (!industriesList) {
      console.error('Industries list element not found!');
      return;
    }

    if (industries.length === 0) {
      console.log('No industries to display');
      industriesList.innerHTML = '<div class="no-results">No industries found</div>';
      return;
    }

    const html = industries.map(industry => {
      const isSelected = window.selectedIndustries.some(selected => selected.id === industry.id);
      return `
        <div class="industry-option ${isSelected ? 'selected disabled' : ''}"
             data-id="${industry.id}"
             data-name="${industry.name}"
             ${isSelected ? 'data-disabled="true"' : ''}>
          ${industry.name}
          ${isSelected ? '<span class="selected-indicator">✓ Selected</span>' : ''}
        </div>
      `;
    }).join('');

    industriesList.innerHTML = html;

    // Add click handlers
    industriesList.querySelectorAll('.industry-option').forEach(option => {
      option.addEventListener('click', function() {
        // Check if this option is disabled (already selected)
        if (this.dataset.disabled === 'true') {
          console.log('Industry already selected, ignoring click');
          return; // Don't allow clicking on already selected industries
        }

        const id = parseInt(this.dataset.id);
        const name = this.dataset.name;

        if (!window.selectedIndustries.some(selected => selected.id === id)) {
          window.addIndustryTag(id, name);

          // Clear search and hide dropdown
          document.getElementById('industries-input').value = '';
          window.hideIndustriesList();
        }
      });
    });
  }

  window.addIndustryTag = function(id, name) {
    console.log('=== ADDING INDUSTRY TAG ===');
    console.log('ID:', id, 'Type:', typeof id);
    console.log('Name:', name);

    // Ensure ID is a number
    const numericId = parseInt(id);
    console.log('Numeric ID:', numericId);

    window.selectedIndustries.push({ id: numericId, name });
    window.renderSelectedIndustries();
    window.updateHiddenInput();

    // Clear the industries error message when an industry is selected
    clearError('industries');

    // Refresh the dropdown to disable the newly selected industry
    const searchTerm = document.getElementById('industries-input').value.trim();
    if (searchTerm) {
      window.filterIndustries(searchTerm);
    } else {
      window.renderIndustriesList(window.allIndustries);
    }

    console.log('Updated selected industries:', window.selectedIndustries);
  };

  window.removeIndustryTag = function(id) {
    console.log('=== REMOVING INDUSTRY TAG ===');
    console.log('Removing industry ID:', id);

    window.selectedIndustries = window.selectedIndustries.filter(industry => industry.id !== id);
    window.renderSelectedIndustries();
    window.updateHiddenInput();

    // Show error message if no industries remain after removal
    if (window.selectedIndustries.length === 0) {
      showError('industries', 'Please select at least one industry.');
    }

    // Refresh the dropdown to re-enable the removed industry
    const searchTerm = document.getElementById('industries-input').value.trim();
    if (searchTerm) {
      window.filterIndustries(searchTerm);
    } else {
      window.renderIndustriesList(window.allIndustries);
    }

    console.log('Updated selected industries:', window.selectedIndustries);
  };

  window.renderSelectedIndustries = function() {
    const container = document.getElementById('selected-industries');
    if (!container) return;

    const html = window.selectedIndustries.map(industry => `
      <div class="industry-tag">
        <span>${industry.name}</span>
        <button type="button" class="remove-tag" onclick="window.removeIndustryTag(${industry.id})">×</button>
      </div>
    `).join('');

    container.innerHTML = html;
  };

  window.updateHiddenInput = function() {
    const hiddenInput = document.getElementById('industries');
    if (hiddenInput) {
      const industryIds = window.selectedIndustries.map(industry => industry.id);
      const hiddenValue = industryIds.join(',');
      hiddenInput.value = hiddenValue;

      console.log('=== UPDATING HIDDEN INPUT ===');
      console.log('Industry IDs:', industryIds);
      console.log('Hidden input value:', hiddenValue);
    }
  };

  window.showIndustriesList = function() {
    document.getElementById('industries-list').style.display = 'block';
  };

  window.hideIndustriesList = function() {
    document.getElementById('industries-list').style.display = 'none';
  };

  window.filterIndustries = function(searchTerm) {
    const filtered = window.allIndustries.filter(industry =>
      industry.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    window.renderIndustriesList(filtered);
  };

  window.initializeIndustriesMultiSelect = function() {
    const input = document.getElementById('industries-input');
    const container = document.querySelector('.industries-select-container');

    console.log('=== INITIALIZING INDUSTRIES MULTI-SELECT ===');
    console.log('Input element:', input);
    console.log('Container element:', container);

    if (!input || !container) {
      console.error('Required elements not found for industries multi-select');
      return;
    }

    // Show dropdown when clicking on container
    container.addEventListener('click', function() {
      input.focus();
      window.showIndustriesList();
    });

    // Handle input events
    input.addEventListener('input', function() {
      const searchTerm = this.value.trim();
      if (searchTerm) {
        window.filterIndustries(searchTerm);
      } else {
        window.renderIndustriesList(window.allIndustries);
      }
      window.showIndustriesList();
    });

    input.addEventListener('focus', function() {
      window.showIndustriesList();
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
      if (!container.contains(e.target)) {
        window.hideIndustriesList();
      }
    });

    console.log('Industries multi-select initialized successfully');
  };

  // Function to redirect with conditional parameter preservation
  function redirectWithParams(url) {
    const urlWithParams = getUrlWithParams(url);
    window.location.href = urlWithParams;
  }

  // Initialize tag-style multi-select for industries
  window.initializeIndustriesMultiSelect();
});
</script>
<?php include plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_footer.php'; ?>
