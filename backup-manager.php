<?php
/*
Template Name: Backup Manager
*/

// Prevent direct access
if (!defined('ABSPATH')) exit;

// Check if user has permission
if (!current_user_can('manage_options') && !current_user_can('edit_pages')) {
    wp_die('You do not have permission to access this page.');
}

get_header();
?>

<div class="backup-manager-container" style="max-width: 1200px; margin: 20px auto; padding: 20px;">
    <h1>All-in-One WP Migration Backup Manager</h1>
    
    <div class="backup-controls" style="margin-bottom: 20px;">
        <button id="refresh-backups" class="button button-primary">Refresh Backup List</button>
        <span id="loading-indicator" style="display: none; margin-left: 10px;">Loading...</span>
    </div>

    <div id="backup-results">
        <p>Click "Refresh Backup List" to load available backup files.</p>
    </div>
</div>

<style>
.backup-manager-container {
    font-family: -apple-system, BlinkMacSystemFont, "Se<PERSON><PERSON> UI", <PERSON><PERSON>, sans-serif;
}

.backup-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.backup-table th,
.backup-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.backup-table th {
    background-color: #f5f5f5;
    font-weight: 600;
}

.backup-table tr:hover {
    background-color: #f9f9f9;
}

.download-btn {
    background-color: #0073aa;
    color: white;
    padding: 6px 12px;
    text-decoration: none;
    border-radius: 3px;
    font-size: 13px;
}

.download-btn:hover {
    background-color: #005a87;
    color: white;
}

.backup-info {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.error-message {
    background-color: #ffebee;
    border: 1px solid #ffcdd2;
    color: #c62828;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.success-message {
    background-color: #e8f5e8;
    border: 1px solid #c8e6c9;
    color: #2e7d32;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Function to load backup files
    function loadBackupFiles() {
        $('#loading-indicator').show();
        $('#refresh-backups').prop('disabled', true);
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'ipt_get_backup_files',
                security: '<?php echo wp_create_nonce('ipt_backup_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    displayBackupFiles(response.data);
                } else {
                    displayError(response.data.message || 'Failed to load backup files');
                }
            },
            error: function(xhr, status, error) {
                displayError('AJAX Error: ' + error);
            },
            complete: function() {
                $('#loading-indicator').hide();
                $('#refresh-backups').prop('disabled', false);
            }
        });
    }
    
    // Function to display backup files
    function displayBackupFiles(data) {
        let html = '';
        
        if (data.total_files === 0) {
            html = '<div class="backup-info">No backup files found in the AI1WM backups directory.</div>';
        } else {
            html += '<div class="backup-info">';
            html += '<strong>Backup Directory:</strong> ' + data.backup_directory + '<br>';
            html += '<strong>Total Files:</strong> ' + data.total_files + '<br>';
            html += '<strong>Directory Status:</strong> ' + (data.directory_exists ? 'Exists' : 'Not Found') + ' | ' + (data.directory_readable ? 'Readable' : 'Not Readable');
            html += '</div>';
            
            html += '<table class="backup-table">';
            html += '<thead>';
            html += '<tr>';
            html += '<th>Filename</th>';
            html += '<th>Size</th>';
            html += '<th>Date Modified</th>';
            html += '<th>Status</th>';
            html += '<th>Actions</th>';
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';
            
            data.backup_files.forEach(function(file) {
                html += '<tr>';
                html += '<td>' + file.filename + '</td>';
                html += '<td>' + file.size_formatted + '</td>';
                html += '<td>' + file.date_modified + '</td>';
                html += '<td>' + (file.is_readable ? '<span style="color: green;">✓ Readable</span>' : '<span style="color: red;">✗ Not Readable</span>') + '</td>';
                html += '<td>';
                if (file.is_readable) {
                    html += '<a href="' + file.download_url + '" class="download-btn" target="_blank">Download</a>';
                } else {
                    html += '<span style="color: #999;">Not Available</span>';
                }
                html += '</td>';
                html += '</tr>';
            });
            
            html += '</tbody>';
            html += '</table>';
        }
        
        $('#backup-results').html(html);
    }
    
    // Function to display error messages
    function displayError(message) {
        let html = '<div class="error-message">';
        html += '<strong>Error:</strong> ' + message;
        html += '</div>';
        $('#backup-results').html(html);
    }
    
    // Refresh button click handler
    $('#refresh-backups').on('click', function() {
        loadBackupFiles();
    });
    
    // Auto-load on page load
    loadBackupFiles();
});
</script>

<?php get_footer(); ?>
