<?php
/**
 * Test Implementation File
 * Add this to your WordPress site to test the new async functionality
 * 
 * Usage: Add this as a shortcode [test_backup_approval] on any page
 */

// Prevent direct access
if (!defined('ABSPATH')) exit;

/**
 * Shortcode to display test interface
 */
function test_backup_approval_shortcode() {
    // Only show to logged-in users with appropriate permissions
    if (!current_user_can('edit_pages')) {
        return '<p>You do not have permission to access this test.</p>';
    }

    ob_start();
    ?>
    <div id="backup-approval-test" style="max-width: 800px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
        <h2>🧪 Backup Approval Implementation Test</h2>
        
        <div style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h3>✅ Implementation Status</h3>
            <ul>
                <li>✅ Modified designer-tagging.php</li>
                <li>✅ Added backup-approval-handler.js</li>
                <li>✅ Created test files</li>
                <li>🔄 Ready for testing</li>
            </ul>
        </div>

        <div style="background: #fff2cc; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h3>⚠️ Before Testing</h3>
            <p><strong>Important:</strong> Make sure you have a recent backup of your site before testing the approval process.</p>
        </div>

        <!-- Test Forms -->
        <div style="background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 15px 0;">
            <h3>Test 1: Quick Approval (No API Backup)</h3>
            <p>This test bypasses the API backup and only tests the approval logic.</p>
            
            <form id="quick-test-form">
                <input type="hidden" name="action" value="ipt_handle_quick_approval">
                <input type="hidden" name="approval_action" value="approve">
                <input type="hidden" name="template_id" value="161">
                <input type="hidden" name="page_url" value="<?php echo esc_url(home_url('/?test=approval')); ?>">
                <input type="hidden" name="page_params" value="test=approval">
                <input type="hidden" name="security" value="<?php echo wp_create_nonce('ipt_approval_nonce'); ?>">
                
                <button type="button" onclick="testQuickApproval()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                    🚀 Test Quick Approval
                </button>
            </form>
            
            <div id="quick-test-result" style="margin-top: 10px; padding: 10px; border-radius: 4px; display: none;"></div>
        </div>

        <div style="background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 15px 0;">
            <h3>Test 2: WordPress Cron Test</h3>
            <p>This test verifies that WordPress cron is working (required for async processing).</p>

            <button type="button" onclick="testWordPressCron()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                ⏰ Test WordPress Cron
            </button>

            <div id="cron-test-result" style="margin-top: 10px; padding: 10px; border-radius: 4px; display: none;"></div>
        </div>

        <div style="background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 15px 0;">
            <h3>Test 3: Async Backup & Approval</h3>
            <p>This test runs the full async process with progress tracking.</p>

            <form id="async-test-form">
                <input type="hidden" name="action" value="ipt_handle_api_backup_and_approval">
                <input type="hidden" name="approval_action" value="approve">
                <input type="hidden" name="template_id" value="161">
                <input type="hidden" name="page_url" value="<?php echo esc_url(home_url('/?test=async_approval')); ?>">
                <input type="hidden" name="page_params" value="test=async_approval">
                <input type="hidden" name="security" value="<?php echo wp_create_nonce('ipt_approval_nonce'); ?>">

                <button type="button" data-action="backup-approval" style="background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                    🔄 Test Async Backup & Approval
                </button>
            </form>

            <div id="async-test-result" style="margin-top: 10px; padding: 10px; border-radius: 4px; display: none;"></div>
        </div>

        <!-- Status Display -->
        <div id="test-status" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <h4>Test Status:</h4>
            <div id="status-messages">Ready to test...</div>
        </div>

        <!-- Debug Information -->
        <div style="background: #f1f1f1; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h4>🔧 Debug Information</h4>
            <ul>
                <li><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></li>
                <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                <li><strong>Plugin Path:</strong> <?php echo plugin_dir_path(__FILE__); ?></li>
                <li><strong>AJAX URL:</strong> <?php echo admin_url('admin-ajax.php'); ?></li>
                <li><strong>Current User:</strong> <?php echo wp_get_current_user()->user_login; ?> (ID: <?php echo get_current_user_id(); ?>)</li>
                <li><strong>User Capabilities:</strong> 
                    <?php 
                    $user = wp_get_current_user();
                    echo current_user_can('edit_pages') ? '✅ edit_pages' : '❌ edit_pages';
                    echo ', ';
                    echo current_user_can('manage_options') ? '✅ manage_options' : '❌ manage_options';
                    ?>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // Test WordPress Cron functionality
        async function testWordPressCron() {
            const resultDiv = document.getElementById('cron-test-result');
            const statusDiv = document.getElementById('status-messages');

            resultDiv.style.display = 'block';
            resultDiv.style.background = '#fff3cd';
            resultDiv.innerHTML = '⏳ Testing WordPress cron...';

            statusDiv.innerHTML = 'Testing WordPress cron functionality...';

            try {
                // Start cron test
                const startResponse = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=test_wp_cron'
                });

                const startResult = await startResponse.json();

                if (!startResult.success) {
                    throw new Error(startResult.data);
                }

                const testId = startResult.data.test_id;
                resultDiv.innerHTML = '⏳ Cron scheduled, waiting for execution...';

                // Wait and check status
                let attempts = 0;
                const maxAttempts = 12; // 60 seconds total

                const checkStatus = async () => {
                    attempts++;

                    const checkResponse = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=check_cron_test&test_id=' + testId
                    });

                    const checkResult = await checkResponse.json();

                    if (checkResult.success && checkResult.data.status === 'executed') {
                        resultDiv.style.background = '#d4edda';
                        resultDiv.innerHTML = '✅ WordPress cron test successful!<br><pre>' + JSON.stringify(checkResult.data, null, 2) + '</pre>';
                        statusDiv.innerHTML = 'WordPress cron test: ✅ PASSED';
                        return;
                    }

                    if (attempts >= maxAttempts) {
                        resultDiv.style.background = '#f8d7da';
                        resultDiv.innerHTML = '❌ Cron test timeout - cron may not be working properly';
                        statusDiv.innerHTML = 'WordPress cron test: ❌ TIMEOUT';
                        return;
                    }

                    // Try again in 5 seconds
                    setTimeout(checkStatus, 5000);
                };

                // Start checking after 10 seconds
                setTimeout(checkStatus, 10000);

            } catch (error) {
                resultDiv.style.background = '#f8d7da';
                resultDiv.innerHTML = '❌ Cron test error: ' + error.message;
                statusDiv.innerHTML = 'WordPress cron test: ❌ ERROR';
                console.error('Cron test error:', error);
            }
        }

        // Test function for quick approval
        async function testQuickApproval() {
            const resultDiv = document.getElementById('quick-test-result');
            const statusDiv = document.getElementById('status-messages');
            
            resultDiv.style.display = 'block';
            resultDiv.style.background = '#fff3cd';
            resultDiv.innerHTML = '⏳ Testing quick approval...';
            
            statusDiv.innerHTML = 'Running quick approval test...';
            
            const form = document.getElementById('quick-test-form');
            const formData = new FormData(form);
            
            try {
                const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.style.background = '#d4edda';
                    resultDiv.innerHTML = '✅ Quick approval test successful!<br><pre>' + JSON.stringify(result.data, null, 2) + '</pre>';
                    statusDiv.innerHTML = 'Quick approval test: ✅ PASSED';
                } else {
                    resultDiv.style.background = '#f8d7da';
                    resultDiv.innerHTML = '❌ Quick approval test failed:<br><pre>' + JSON.stringify(result.data, null, 2) + '</pre>';
                    statusDiv.innerHTML = 'Quick approval test: ❌ FAILED';
                }
            } catch (error) {
                resultDiv.style.background = '#f8d7da';
                resultDiv.innerHTML = '❌ Network error: ' + error.message;
                statusDiv.innerHTML = 'Quick approval test: ❌ NETWORK ERROR';
                console.error('Test error:', error);
            }
        }

        // Update status messages for async test
        document.addEventListener('DOMContentLoaded', function() {
            // Override the success/error handlers to update our test status
            if (window.backupApprovalHandler) {
                const originalShowSuccess = window.backupApprovalHandler.showSuccess;
                const originalShowError = window.backupApprovalHandler.showError;
                
                window.backupApprovalHandler.showSuccess = function(data) {
                    document.getElementById('status-messages').innerHTML = 'Async backup test: ✅ COMPLETED';
                    originalShowSuccess.call(this, data);
                };
                
                window.backupApprovalHandler.showError = function(message) {
                    document.getElementById('status-messages').innerHTML = 'Async backup test: ❌ FAILED - ' + message;
                    originalShowError.call(this, message);
                };
            }
        });
    </script>
    <?php
    return ob_get_clean();
}

// Register the shortcode
add_shortcode('test_backup_approval', 'test_backup_approval_shortcode');

/**
 * Test WordPress cron functionality
 */
function test_wp_cron_functionality() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Permission denied');
        return;
    }

    // Schedule a test event
    $test_id = 'cron_test_' . time();
    wp_schedule_single_event(time() + 5, 'test_cron_event', array($test_id));

    // Store test data
    set_transient('cron_test_' . $test_id, array(
        'status' => 'scheduled',
        'scheduled_at' => current_time('mysql')
    ), 300);

    wp_send_json_success(array(
        'message' => 'Cron test scheduled',
        'test_id' => $test_id
    ));
}
add_action('wp_ajax_test_wp_cron', 'test_wp_cron_functionality');

/**
 * Handle test cron event
 */
function handle_test_cron_event($test_id) {
    $test_data = get_transient('cron_test_' . $test_id);
    if ($test_data) {
        $test_data['status'] = 'executed';
        $test_data['executed_at'] = current_time('mysql');
        set_transient('cron_test_' . $test_id, $test_data, 300);
        error_log("✅ Cron test executed successfully: " . $test_id);
    }
}
add_action('test_cron_event', 'handle_test_cron_event');

/**
 * Check cron test status
 */
function check_cron_test_status() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Permission denied');
        return;
    }

    $test_id = sanitize_text_field($_POST['test_id']);
    $test_data = get_transient('cron_test_' . $test_id);

    if (!$test_data) {
        wp_send_json_error('Test not found');
        return;
    }

    wp_send_json_success($test_data);
}
add_action('wp_ajax_check_cron_test', 'check_cron_test_status');

/**
 * Add admin notice with instructions
 */
function backup_approval_test_admin_notice() {
    if (current_user_can('manage_options')) {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>Backup Approval Test Ready!</strong> ';
        echo 'Add the shortcode <code>[test_backup_approval]</code> to any page to test the new async functionality.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'backup_approval_test_admin_notice');
?>
