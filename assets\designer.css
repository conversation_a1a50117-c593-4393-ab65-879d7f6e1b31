/* assets/designer.css */

/* Plugin Topbar - Ensure it appears above WordPress admin toolbar */
#plugin-topbar {
  position: relative !important;
  z-index: 100000 !important;
}

/* Ensure plugin topbar is above admin bar in all contexts */
body.admin-bar #plugin-topbar {
  z-index: 100001 !important;
}

/* Fix for when admin bar is present */
#wpadminbar {
  z-index: 99999 !important;
}

/* Page Section Sidebar Styling */
#pageSectionSidebar {
  width: 300px;
  background: white;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Regular page items hover */
#pageSectionSidebar .page-list-item > div {
  transition: background-color 0.2s ease;
}

#pageSectionSidebar .page-list-item > div:hover {
  background-color: #4DCDB4 !important;
  color: white !important;
}
#pageSectionSidebar .bg-primary {
    background-color: #4DCDB4 !important; /* Keep original blue */
  color: white !important;
}
/* Homepage (current page) - keep blue background, only change hover */
#pageSectionSidebar .page-list-item.bg-primary > div {
  background-color: #4DCDB4 !important; /* Keep original blue */
  color: white !important;
}

#pageSectionSidebar .page-list-item.bg-primary > div:hover {
  background-color: #4DCDB4 !important; /* Darker blue on hover */
  color: white !important;
}

/* Update text color on hover for better contrast */
#pageSectionSidebar .page-list-item > div:hover span,
#pageSectionSidebar .page-list-item > div:hover i {
  color: white !important;
}

/* Menu button hover effect */
#pageSectionSidebar .page-menu-btn:hover {
  background-color: rgba(255,255,255,0.2) !important;
  border-radius: 4px;
}

#pageSectionSidebar .page-list-item.bg-primary .page-menu-btn:hover {
  background-color: rgba(255,255,255,0.3) !important;
}

/* Menu button icon color on parent hover */
#pageSectionSidebar .page-list-item > div:hover .page-menu-btn i {
  color: white !important;
}

#pageSectionSidebar .page-context-menu {
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  z-index: 9999 !important;
  border: 1px solid #e0e0e0;
  max-height: 300px;
  overflow-y: auto;
}

#pageSectionSidebar .page-context-menu .dropdown-item:hover {
  background-color: #f8f9fa;
}

#pageSectionSidebar .page-context-menu .dropdown-item:hover[style*="color: #dc3545"] {
  background-color: #f8d7da;
}

/* Search input styling */
#page-search:focus {
  outline: none;
  border-color: #4DCDB4;
  box-shadow: 0 0 0 0.2rem rgba(77, 205, 180, 0.25);
}
[data-designer-editable] {
    outline: 2px dashed #4DCDB4 !important;
    outline-offset: 2px;
    cursor: pointer;
}
#designer-context-menu {
    position: absolute;
    z-index: 999999 !important;
    background: #fff;
    border: 1px solid #ccc;
    padding: 12px 16px;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    display: none;
    min-width: 250px; /* Đảm bảo menu đủ rộng */
    max-width: 300px; /* Giới hạn chiều rộng tối đa */
}

/* Thêm mũi tên chỉ lên trên cho context menu */
#designer-context-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 15px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #fff;
    z-index: 2;
}

#designer-context-menu::after {
    content: '';
    position: absolute;
    top: -9px;
    left: 15px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #ccc;
    z-index: 1;
}

/* Overlay cho các phần tử media */
.media-tag-overlay {
    position: absolute;
    background: rgba(37, 99, 235, 0.5);
    color: white;
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9998;
    pointer-events: all;
    font-size: 16px;
    font-weight: bold;
    border: 2px solid #4DCDB4;
}
.btn-primary {
    background-color: #4DCDB4 !important;
    border-color: #4DCDB4 !important;
}

.media-tag-content {
    background: rgba(0, 0, 0, 0.7);
    padding: 8px 12px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    pointer-events: auto;
}

.media-tag-type {
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.media-tag-btn {
    background: #4DCDB4;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
}

.media-tag-btn:hover {
    background: #1d4ed8;
}

/* Overlay để ngăn tương tác với iframe và video */
.iframe-overlay, .video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: 99999 !important; /* Z-index cao nhất */
    cursor: pointer;
    pointer-events: all !important; /* Đảm bảo chặn tương tác */
}

/* Hiệu ứng hover cho overlay */
.auto-detect-tag-overlay:hover {
    background: rgba(37, 99, 235, 0.3);
    border: 2px dashed #2563eb;
}

/* Đảm bảo iframe và video có position relative để ::before hoạt động đúng */
iframe.auto-detect-tag, 
video.auto-detect-tag {
    position: relative;
}

/* Overlay cho các phần tử media đã được đánh dấu */
.tagged-media-overlay {
    position: absolute;
    background: rgba(37, 99, 235, 0.2);
    border: 2px solid #4DCDB4;
    z-index: 99999 !important; /* Z-index cao nhất */
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: all !important; /* Chặn tương tác với phần tử bên dưới */
    cursor: default !important;
    box-sizing: border-box !important;
}

.tagged-label {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
    user-select: none !important;
    z-index: 100000 !important; /* Z-index cao hơn overlay */
}

/* Media tag overlay */
#media-tag-overlay {
    position: absolute;
    z-index: 100001 !important; /* Z-index cao hơn overlay */
    display: none;
    justify-content: flex-start;
    align-items: flex-start;
    pointer-events: all; /* Thay đổi từ none thành all để bắt sự kiện hover */
    padding: 10px;
}

.media-tag-content {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* Căn lề trái thay vì center */
    gap: 5px;
    pointer-events: all; /* Cho phép tương tác với nút */
    margin-top: 5px;
    margin-left: 5px;
}

.media-tag-type {
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.media-tag-btn {
    background: #4DCDB4;
    color: white;
    border: none;
    padding: 8px 15px; /* Tăng padding để dễ click hơn */
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
    z-index: 100002 !important; /* Z-index cao nhất */
    align-self: flex-start;
    min-width: 100px; /* Đảm bảo nút đủ rộng để dễ click */
    text-align: center;
}

.media-tag-btn:hover {
    background: #3ab9a0;
    transform: translateY(-1px); /* Hiệu ứng nhẹ khi hover */
    box-shadow: 0 2px 4px rgba(0,0,0,0.2); /* Thêm shadow khi hover */
}

/* Thêm hiệu ứng active khi click */
.media-tag-btn:active {
    transform: translateY(1px);
    box-shadow: none;
}

/* Đảm bảo overlay hiển thị đúng trên các iframe */
.auto-detect-tag-overlay {
    pointer-events: all !important; /* Chặn tương tác với phần tử bên dưới */
    cursor: default !important;
}

.designer-highlight {
    outline: 2px dotted #4DCDB4 !important;
    outline-offset: 2px;
    z-index: 9999;
    /* position: relative; */
    box-shadow: none !important;
}

/* Highlight khi scroll đến phần tử */
.designer-highlight-active {
    animation: flash-highlight 2s ease-in-out;
    outline: 3px solid #ff5722 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 15px rgba(255, 87, 34, 0.7) !important;
    z-index: 99998 !important; /* Z-index cao nhưng thấp hơn overlay */
}

@keyframes flash-highlight {
    0%, 100% {
        outline-color: #ff5722;
        box-shadow: 0 0 15px rgba(255, 87, 34, 0.7);
    }
    50% {
        outline-color: #ffeb3b;
        box-shadow: 0 0 20px rgba(255, 235, 59, 0.9);
    }
}

/* Style cho accordion khi được click */
.accordion {
    cursor: pointer;
    padding: 10px;
    width: 100%;
    text-align: left;
    border: none;
    outline: none;
    transition: 0.4s;
    background-color: #f1f1f1;
    border-radius: 4px;
    margin-bottom: 5px;
    font-weight: 500;
}

.accordion:hover {
    background-color: #e0e0e0;
}

.accordion.active {
    background-color: #4DCDB4;
    color: white;
}

/* Thêm style cho section header */
.section-header {
    margin-top: 15px;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e0e0e0;
}

.section-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    padding: 0;
}

/* Thêm margin cho accordion đầu tiên trong mỗi section */
.section-header + .accordion {
    margin-top: 8px;
}

/* Style cho accordion actions */
.accordion-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

/* Style cho accordion arrow - làm cho dễ click hơn */
.accordion-arrow {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    user-select: none;
}

.accordion-arrow:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.accordion-arrow i {
    font-size: 14px;
    transition: transform 0.3s ease;
    pointer-events: none; /* Đảm bảo click event đi đến parent */
}

/* Xoay icon khi accordion active */
.accordion.active .accordion-arrow i {
    transform: rotate(180deg);
}

/* Style cho accordion delete button */
.accordion-delete {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    color: #dc3545;
    user-select: none;
}

.accordion-delete:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

.accordion-delete i {
    font-size: 12px;
    pointer-events: none; /* Đảm bảo click event đi đến parent */
}

/* Cải thiện layout của accordion */
.accordion {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.accordion-title {
    flex: 1;
    margin-right: 10px;
}

/* Style cho panel */
.panel {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 0 !important;
    overflow: hidden;
    transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
    opacity: 0 !important;
}

.panel.active {
    max-height: 500px !important; /* Đủ lớn để chứa nội dung */
    opacity: 1 !important;
}

/* Style cho nội dung bên trong panel */
.panel .tag-head {
    padding: 15px;
}

.panel .form-group {
    margin-bottom: 12px;
}

.panel .form-group:last-child {
    margin-bottom: 0;
}

.panel label {
    display: block;
    font-weight: 500;
    margin-bottom: 4px;
    color: #495057;
    font-size: 12px;
}

.panel input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    font-size: 13px;
    background-color: #fff;
}

.panel input:focus {
    outline: none;
    border-color: #4DCDB4;
    box-shadow: 0 0 0 2px rgba(77, 205, 180, 0.2);
}

.panel input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

/* Toast message */
.designer-toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    min-width: 250px;
    max-width: 350px;
    padding: 12px 16px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 100000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.designer-toast.show {
    transform: translateY(0);
    opacity: 1;
}

.designer-toast-success {
    background-color: #4DCDB4;
}

.designer-toast-error {
    background-color: #f44336;
}

.designer-toast-info {
    background-color: #2196f3;
}

.designer-toast-warning {
    background-color: #ff9800;
}

/* Master Tagging Submenu */
#master-tagging-item {
    position: relative;
}

#master-tagging-submenu {
    min-width: 180px;
}

/* Hiệu ứng hover cho các item trong submenu */
.master-tag-item:hover {
    background-color: #f3f4f6;
}

/* Style cho master tag đã được sử dụng */
.master-tag-disabled {
    opacity: 0.5;
    cursor: not-allowed !important;
    background-color: #f5f5f5 !important;
    color: #999 !important;
}

.master-tag-disabled:hover {
    background-color: #f5f5f5 !important;
    color: #999 !important;
}

.tag-used-indicator {
    font-size: 11px;
    font-style: italic;
    color: #666;
}



/* Validation error styling */
.designer-type-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
}

.validation-message {
    font-size: 11px;
    color: #dc3545;
    margin-top: 4px;
    display: none;
}
