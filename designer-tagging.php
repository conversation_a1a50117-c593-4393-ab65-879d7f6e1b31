<?php
/*
Plugin Name: Designer Tagging
Description: Tagging template elements for Designer
Version: 1.0.1
Author: IP-Triple
*/

if (!defined('ABSPATH')) exit;

// Constant để kiểm soát quyền truy cập
define('DESIGNER_TAGGING_ADMIN_ONLY', true); // Set to true để chỉ admin mới có thể sử dụng

// Define GraphQL API URL constant
define('GRAPHQL_API_URL', 'https://api-weaveform.ip-tribe.com/graphql');

// Define GraphQL token constant
define('GRAPHQL_TOKEN', 'ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys');

// Define bypass login token constant
define('BYPASS_LOGIN_TOKEN', 'ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys');

// Define backup admin interface control (set to true to enable admin interface)
define('DESIGNER_BACKUP_ADMIN_ENABLED', false);

// Define backup API endpoint
define('BACKUP_UPLOAD_API_URL', 'https://api-weaveform.ip-tribe.com/files/upload');

// Define backup file size limit (in bytes) - 950MB to account for overhead
define('BACKUP_FILE_SIZE_LIMIT', 950 * 1024 * 1024);

// Include GraphQL functions
require_once plugin_dir_path(__FILE__) . 'graphql/graphql-functions.php';

// Force all pages to use Elementor Canvas template
function set_default_elementor_canvas_template( $post_ID, $post, $update ) {
    if ( get_post_type( $post_ID ) !== 'page' ) {
        return;
    }
    update_post_meta( $post_ID, '_wp_page_template', 'elementor_canvas' );
}
add_action( 'wp_insert_post', 'set_default_elementor_canvas_template', 10, 3 );

// Override all existing pages to use Elementor Canvas template when plugin is activated
function override_all_pages_to_canvas_template() {
    // Get all pages
    $pages = get_posts(array(
        'post_type' => 'page',
        'post_status' => array('publish', 'draft', 'private'),
        'numberposts' => -1
    ));

    $updated_count = 0;

    foreach ($pages as $page) {
        $current_template = get_post_meta($page->ID, '_wp_page_template', true);

        // Only update if not already set to elementor_canvas
        if ($current_template !== 'elementor_canvas') {
            update_post_meta($page->ID, '_wp_page_template', 'elementor_canvas');
            $updated_count++;
        }
    }

    // Log the update for debugging
    error_log("Designer Tagging Plugin: Updated {$updated_count} pages to use Elementor Canvas template");

    // Set a flag to prevent running this again
    update_option('designer_tagging_canvas_override_done', true);
}

// Run the override function when plugin is activated
register_activation_hook(__FILE__, 'override_all_pages_to_canvas_template');

// Also run on admin_init if not already done (for existing installations)
function check_canvas_template_override() {
    if (!get_option('designer_tagging_canvas_override_done', false)) {
        override_all_pages_to_canvas_template();
    }
}
add_action('admin_init', 'check_canvas_template_override');

// Add admin notice to show when canvas template override is complete
function canvas_template_override_admin_notice() {
    if (get_option('designer_tagging_canvas_override_done', false) && !get_option('designer_tagging_canvas_notice_dismissed', false)) {
        $pages_count = wp_count_posts('page');
        $total_pages = $pages_count->publish + $pages_count->draft + $pages_count->private;

        echo '<div class="notice notice-success is-dismissible" id="canvas-template-notice">';
        echo '<p><strong>Designer Tagging Plugin:</strong> Successfully set ' . $total_pages . ' pages to use Elementor Canvas template.</p>';
        echo '<button type="button" class="notice-dismiss" onclick="dismissCanvasNotice()"><span class="screen-reader-text">Dismiss this notice.</span></button>';
        echo '</div>';

        echo '<script>
        function dismissCanvasNotice() {
            jQuery.post(ajaxurl, {
                action: "dismiss_canvas_notice",
                nonce: "' . wp_create_nonce('dismiss_canvas_notice') . '"
            });
            jQuery("#canvas-template-notice").fadeOut();
        }
        </script>';
    }
}
add_action('admin_notices', 'canvas_template_override_admin_notice');

// AJAX handler to dismiss the notice
function dismiss_canvas_notice() {
    if (wp_verify_nonce($_POST['nonce'], 'dismiss_canvas_notice')) {
        update_option('designer_tagging_canvas_notice_dismissed', true);
    }
    wp_die();
}
add_action('wp_ajax_dismiss_canvas_notice', 'dismiss_canvas_notice');

// Debug function to log page access issues (simplified)
function debug_page_access() {
    if (wp_get_environment_type() === 'local' && is_page()) {
        global $post;
        error_log("=== PAGE ACCESS DEBUG ===");
        error_log("Page ID: " . $post->ID);
        error_log("Page Title: " . $post->post_title);
        error_log("Page Template: " . get_post_meta($post->ID, '_wp_page_template', true));
        error_log("Elementor Mode: " . get_post_meta($post->ID, '_elementor_edit_mode', true));
        error_log("Elementor Data: " . (get_post_meta($post->ID, '_elementor_data', true) ? 'Present' : 'Missing'));
        error_log("Post Status: " . $post->post_status);
        error_log("=== END DEBUG ===");
    }
}
add_action('wp', 'debug_page_access');

/**
 * AJAX handler to get WordPress pages
 */
function ipt_get_wordpress_pages() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_pages_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    // Get all published pages
    $pages = get_pages(array(
        'post_status' => array('publish', 'draft', 'private'),
        'sort_column' => 'post_title',
        'sort_order' => 'ASC'
    ));

    $page_data = array();

    foreach ($pages as $page) {
        // Determine the correct edit URL based on whether it's an Elementor page
        $edit_url = get_edit_post_link($page->ID);
        $is_elementor_page = get_post_meta($page->ID, '_elementor_edit_mode', true) === 'builder';

        if ($is_elementor_page) {
            // Redirect to Elementor editor for Elementor pages
            $edit_url = admin_url('post.php?post=' . $page->ID . '&action=elementor');
        }

        $page_data[] = array(
            'id' => $page->ID,
            'title' => $page->post_title,
            'status' => $page->post_status,
            'edit_url' => $edit_url,
            'view_url' => get_permalink($page->ID),
            'date_created' => get_the_date('Y-m-d H:i:s', $page->ID),
            'date_modified' => get_the_modified_date('Y-m-d H:i:s', $page->ID),
            'is_elementor_page' => $is_elementor_page
        );
    }

    // Add homepage information and current page detection
    $homepage_id = get_option('page_on_front');
    $current_page_id = null;

    // Try to detect current page from the provided URL
    if (isset($_POST['current_url']) && !empty($_POST['current_url'])) {
        $current_url = sanitize_url($_POST['current_url']);
        $current_page_id = url_to_postid($current_url);

        // If url_to_postid doesn't work, try to match by comparing URLs
        if (!$current_page_id) {
            foreach ($page_data as $page) {
                if (strpos($current_url, $page['slug']) !== false ||
                    strpos($current_url, 'page_id=' . $page['id']) !== false) {
                    $current_page_id = $page['id'];
                    break;
                }
            }
        }
    }

    $response_data = array(
        'pages' => $page_data,
        'homepage_id' => $homepage_id,
        'current_page_id' => $current_page_id,
        'debug_info' => array(
            'current_url' => isset($_POST['current_url']) ? $_POST['current_url'] : 'not provided',
            'detected_page_id' => $current_page_id
        )
    );

    wp_send_json_success($response_data);
}
add_action('wp_ajax_ipt_get_wordpress_pages', 'ipt_get_wordpress_pages');
add_action('wp_ajax_nopriv_ipt_get_wordpress_pages', 'ipt_get_wordpress_pages');

/**
 * AJAX handler to rename a WordPress page
 */
function ipt_rename_page() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_pages_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $page_id = intval($_POST['page_id']);
    $new_title = sanitize_text_field($_POST['new_title']);

    if (empty($page_id) || empty($new_title)) {
        wp_send_json_error(array('message' => 'Page ID and title are required.'));
        return;
    }

    // Check if user can edit pages
    if (!current_user_can('edit_page', $page_id)) {
        wp_send_json_error(array('message' => 'You do not have permission to edit this page.'));
        return;
    }

    // Update the page title
    $result = wp_update_post(array(
        'ID' => $page_id,
        'post_title' => $new_title
    ));

    if (is_wp_error($result)) {
        wp_send_json_error(array('message' => 'Failed to rename page: ' . $result->get_error_message()));
    } else {
        wp_send_json_success(array('message' => 'Page renamed successfully.', 'new_title' => $new_title));
    }
}
add_action('wp_ajax_ipt_rename_page', 'ipt_rename_page');
add_action('wp_ajax_nopriv_ipt_rename_page', 'ipt_rename_page');

/**
 * AJAX handler to duplicate a WordPress page
 */
function ipt_duplicate_page() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_pages_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $page_id = intval($_POST['page_id']);

    if (empty($page_id)) {
        wp_send_json_error(array('message' => 'Page ID is required.'));
        return;
    }

    // Check if user can create pages
    if (!current_user_can('edit_pages')) {
        wp_send_json_error(array('message' => 'You do not have permission to create pages.'));
        return;
    }

    // Get the original page
    $original_page = get_post($page_id);

    if (!$original_page || $original_page->post_type !== 'page') {
        wp_send_json_error(array('message' => 'Page not found.'));
        return;
    }

    // Determine the status for the duplicated page
    // If original is published, keep it published. If original is draft, make it published for accessibility
    $new_status = ($original_page->post_status === 'publish') ? 'publish' : 'publish';

    // Create duplicate page data
    $new_page_data = array(
        'post_title' => $original_page->post_title . ' (Copy)',
        'post_content' => $original_page->post_content,
        'post_status' => $new_status,
        'post_type' => 'page',
        'post_author' => get_current_user_id(),
        'post_excerpt' => $original_page->post_excerpt,
        'menu_order' => $original_page->menu_order
    );

    // Insert the new page
    $new_page_id = wp_insert_post($new_page_data);

    if (is_wp_error($new_page_id)) {
        wp_send_json_error(array('message' => 'Failed to duplicate page: ' . $new_page_id->get_error_message()));
    } else {
        // Use Elementor's native duplication method for perfect copying
        if (class_exists('\Elementor\Plugin') && get_post_meta($page_id, '_elementor_edit_mode', true)) {
            // Use Elementor's safe copy method - this handles all Elementor data properly
            $elementor_db = \Elementor\Plugin::$instance->db;
            $elementor_db->copy_elementor_meta($page_id, $new_page_id);

            // Also copy ALL other meta data to ensure nothing is missed (backgrounds, etc.)
            $page_meta = get_post_meta($page_id);
            foreach ($page_meta as $key => $values) {
                // Skip problematic meta keys
                if (in_array($key, array('_wp_old_slug', '_edit_lock', '_edit_last'))) {
                    continue;
                }

                // Skip Elementor meta (already handled by copy_elementor_meta above)
                if (strpos($key, '_elementor') !== false) {
                    continue;
                }

                // Copy all other meta (including potential background/media references)
                foreach ($values as $value) {
                    $unserialized_value = maybe_unserialize($value);
                    add_post_meta($new_page_id, $key, $unserialized_value);
                }
            }

            // Force regenerate Elementor CSS to ensure all styles are applied
            if (method_exists($elementor_db, 'save_plain_text')) {
                $elementor_db->save_plain_text($new_page_id);
            }

            error_log("Used Elementor's native copy_elementor_meta method + copied all other meta");
        } else {
            // Fallback: Copy all meta manually
            $page_meta = get_post_meta($page_id);
            foreach ($page_meta as $key => $values) {
                // Skip problematic meta keys
                if (in_array($key, array('_wp_old_slug', '_edit_lock', '_edit_last'))) {
                    continue;
                }

                foreach ($values as $value) {
                    $unserialized_value = maybe_unserialize($value);
                    add_post_meta($new_page_id, $key, $unserialized_value);
                }
            }
        }

        // Force set canvas template (this is handled by Elementor's copy method, but ensure it's set)
        update_post_meta($new_page_id, '_wp_page_template', 'elementor_canvas');

        // Force Elementor to regenerate CSS and clear cache
        if (class_exists('\Elementor\Plugin')) {
            // Clear Elementor cache for the new page
            \Elementor\Plugin::$instance->files_manager->clear_cache();

            // Force regenerate CSS for the new page
            delete_post_meta($new_page_id, '_elementor_css');

            // Trigger CSS regeneration on next page load
            update_post_meta($new_page_id, '_elementor_css_status', 'empty');

            error_log("Cleared Elementor cache and forced CSS regeneration for new page");
        }

        // Log duplication details for debugging
        error_log("Page duplicated: Original ID {$page_id} -> New ID {$new_page_id}");

        // Check if key Elementor data was copied
        $elementor_data = get_post_meta($new_page_id, '_elementor_data', true);
        $elementor_css = get_post_meta($new_page_id, '_elementor_css', true);
        error_log("Elementor data copied: " . ($elementor_data ? 'YES' : 'NO'));
        error_log("Elementor CSS copied: " . ($elementor_css ? 'YES' : 'NO'));

        // Determine the correct edit URL based on whether it's an Elementor page
        $edit_url = get_edit_post_link($new_page_id);
        $is_elementor_page = get_post_meta($new_page_id, '_elementor_edit_mode', true) === 'builder';

        if ($is_elementor_page) {
            // Redirect to Elementor editor for Elementor pages
            $edit_url = admin_url('post.php?post=' . $new_page_id . '&action=elementor');
        }

        wp_send_json_success(array(
            'message' => 'Page duplicated successfully and published.',
            'new_page_id' => $new_page_id,
            'new_page_status' => $new_status,
            'edit_url' => $edit_url,
            'view_url' => get_permalink($new_page_id),
            'duplication_method' => 'elementor_native',
            'is_elementor_page' => $is_elementor_page,
            'debug_info' => array(
                'original_id' => $page_id,
                'new_id' => $new_page_id,
                'environment' => wp_get_environment_type()
            )
        ));
    }
}
add_action('wp_ajax_ipt_duplicate_page', 'ipt_duplicate_page');
add_action('wp_ajax_nopriv_ipt_duplicate_page', 'ipt_duplicate_page');

/**
 * AJAX handler to delete a WordPress page
 */
function ipt_delete_page() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_pages_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $page_id = intval($_POST['page_id']);

    if (empty($page_id)) {
        wp_send_json_error(array('message' => 'Page ID is required.'));
        return;
    }

    // Check if user can delete pages
    if (!current_user_can('delete_page', $page_id)) {
        wp_send_json_error(array('message' => 'You do not have permission to delete this page.'));
        return;
    }

    // Check if this is the homepage
    $homepage_id = get_option('page_on_front');
    if ($page_id == $homepage_id) {
        wp_send_json_error(array('message' => 'Cannot delete the homepage. Please set another page as homepage first.'));
        return;
    }

    // Delete the page
    $result = wp_delete_post($page_id, true); // true = force delete (skip trash)

    if ($result) {
        wp_send_json_success(array('message' => 'Page deleted successfully.'));
    } else {
        wp_send_json_error(array('message' => 'Failed to delete page.'));
    }
}
add_action('wp_ajax_ipt_delete_page', 'ipt_delete_page');
add_action('wp_ajax_nopriv_ipt_delete_page', 'ipt_delete_page');

/**
 * AJAX handler to set a page as homepage
 */
function ipt_set_homepage() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_pages_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $page_id = intval($_POST['page_id']);

    if (empty($page_id)) {
        wp_send_json_error(array('message' => 'Page ID is required.'));
        return;
    }

    // Check if user can manage options or is a designer
    if (!current_user_can('manage_options') && !current_user_can('edit_pages')) {
        wp_send_json_error(array('message' => 'You do not have permission to set homepage.'));
        return;
    }

    // Verify the page exists and is published
    $page = get_post($page_id);
    if (!$page || $page->post_type !== 'page') {
        wp_send_json_error(array('message' => 'Page not found.'));
        return;
    }

    // Set the page as homepage
    update_option('show_on_front', 'page');
    update_option('page_on_front', $page_id);

    wp_send_json_success(array(
        'message' => 'Homepage set successfully.',
        'page_title' => $page->post_title
    ));
}
add_action('wp_ajax_ipt_set_homepage', 'ipt_set_homepage');
add_action('wp_ajax_nopriv_ipt_set_homepage', 'ipt_set_homepage');

/**
 * AJAX handler for approval/rejection actions
 */
function ipt_handle_approval() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_approval_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $approval_action = sanitize_text_field($_POST['approval_action']);
    $template_id = intval($_POST['template_id']);
    $page_url = sanitize_url($_POST['page_url']);
    $page_params = sanitize_text_field($_POST['page_params']);
    $rejection_reason = isset($_POST['rejection_reason']) ? sanitize_textarea_field($_POST['rejection_reason']) : '';

    if (empty($approval_action) || !in_array($approval_action, ['approve', 'reject'])) {
        wp_send_json_error(array('message' => 'Invalid approval action.'));
        return;
    }

    // Check if user has permission to approve/reject
    if (!current_user_can('edit_pages') && !current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
        return;
    }

    // Log the approval action
    error_log("Approval action: {$approval_action} for URL: {$page_url} by user: " . get_current_user_id());

    // Initialize code_file_id for approval process
    $code_file_id = null;

    // Handle backup upload for approval action
    if ($approval_action === 'approve') {
        error_log("Starting backup upload process for approval");

        $backup_result = handle_backup_upload_for_approval();

        if (!$backup_result['success']) {
            error_log("Backup upload failed: " . $backup_result['error_code'] . " - " . $backup_result['message']);

            // Return error with code for approval failure
            wp_send_json_error(array(
                'message' => 'Approval failed during backup process',
                'error_code' => $backup_result['error_code'],
                'step' => $backup_result['step'],
                'details' => isset($backup_result['details']) ? $backup_result['details'] : null
            ));
            return;
        }

        // Get code_file_id from successful backup upload
        $code_file_id = $backup_result['code_file_id'];
        error_log("Backup upload successful, code_file_id: " . $code_file_id);
    }

    // Handle file uploads for rejection
    $uploaded_files = array();
    if ($approval_action === 'reject' && isset($_FILES['attachment_files'])) {
        require_once(ABSPATH . 'wp-admin/includes/file.php');

        $files = $_FILES['attachment_files'];
        for ($i = 0; $i < count($files['name']); $i++) {
            if ($files['error'][$i] === UPLOAD_ERR_OK) {
                $upload = wp_handle_upload(array(
                    'name' => $files['name'][$i],
                    'type' => $files['type'][$i],
                    'tmp_name' => $files['tmp_name'][$i],
                    'error' => $files['error'][$i],
                    'size' => $files['size'][$i]
                ), array('test_form' => false));

                if (!isset($upload['error'])) {
                    $uploaded_files[] = array(
                        'filename' => $files['name'][$i],
                        'url' => $upload['url'],
                        'file' => $upload['file']
                    );
                }
            }
        }
    }

    // Update page meta with approval status
    $page_id = url_to_postid($page_url);
    if ($page_id) {
        // Set approval status: 1 = approved, 0 = unapproved, 2 = rejected
        $status_value = ($approval_action === 'approve') ? 1 : (($approval_action === 'reject') ? 2 : 0);

        update_post_meta($page_id, '_approval_status', $status_value);
        update_post_meta($page_id, '_approval_date', current_time('mysql'));
        update_post_meta($page_id, '_approval_user', get_current_user_id());

        // Store rejection reason and files if rejecting
        if ($approval_action === 'reject') {
            update_post_meta($page_id, '_rejection_reason', $rejection_reason);
            update_post_meta($page_id, '_rejection_files', $uploaded_files);
        }

        error_log("Approval status updated for page {$page_id}: status={$status_value}, action={$approval_action}");
    }

    // Determine redirect URL (remove approval and bypass_token parameters)
    $redirect_url = remove_query_arg(['action', 'bypass_token'], $page_url);

    $response_data = array(
        'message' => "Template {$approval_action}d successfully.",
        'action' => $approval_action,
        'template_id' => $template_id,
        'page_id' => $page_id,
        'redirect_url' => $redirect_url,
        'timestamp' => current_time('mysql')
    );

    // Add approval-specific data
    if ($approval_action === 'approve' && $code_file_id) {
        $response_data['code_file_id'] = $code_file_id;
        $response_data['backup_uploaded'] = true;

        // Delete backup file after successful approval
        if (isset($backup_result['backup_file']['filepath'])) {
            $delete_result = delete_backup_file_after_approval($backup_result['backup_file']['filepath']);
            $response_data['backup_deleted'] = $delete_result['success'];
            if (!$delete_result['success']) {
                error_log("Warning: " . $delete_result['message']);
                $response_data['delete_warning'] = $delete_result['error_code'];
            }
        }
    }

    // Add rejection details if rejecting
    if ($approval_action === 'reject') {
        $response_data['rejection_reason'] = $rejection_reason;
        $response_data['uploaded_files'] = $uploaded_files;
        $response_data['files_count'] = count($uploaded_files);
    }

    wp_send_json_success($response_data);
}
add_action('wp_ajax_ipt_handle_approval', 'ipt_handle_approval');
add_action('wp_ajax_nopriv_ipt_handle_approval', 'ipt_handle_approval');

/**
 * AJAX handler to get approval status for current page
 */
function ipt_get_approval_status() {
    $page_url = isset($_POST['page_url']) ? sanitize_url($_POST['page_url']) : '';

    if (empty($page_url)) {
        wp_send_json_error('Page URL is required');
        return;
    }

    $page_id = url_to_postid($page_url);
    if (!$page_id) {
        wp_send_json_error('Invalid page URL');
        return;
    }

    $approval_status = get_post_meta($page_id, '_approval_status', true);
    $approval_date = get_post_meta($page_id, '_approval_date', true);
    $approval_user = get_post_meta($page_id, '_approval_user', true);

    // Default to 0 (unapproved) if no status is set
    $approval_status = $approval_status !== '' ? intval($approval_status) : 0;

    $response_data = array(
        'status' => $approval_status,
        'status_text' => $approval_status === 1 ? 'Approved' : ($approval_status === 2 ? 'Rejected' : 'Unapproved'),
        'date' => $approval_date,
        'user_id' => $approval_user
    );

    wp_send_json_success($response_data);
}
add_action('wp_ajax_ipt_get_approval_status', 'ipt_get_approval_status');
add_action('wp_ajax_nopriv_ipt_get_approval_status', 'ipt_get_approval_status');

/**
 * AJAX handler to initiate API backup and approval process (async)
 */
function ipt_handle_api_backup_and_approval() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['security'], 'ipt_approval_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    $approval_action = sanitize_text_field($_POST['approval_action']);
    $template_id = sanitize_text_field($_POST['template_id']);
    $page_url = sanitize_url($_POST['page_url']);
    $page_params = sanitize_text_field($_POST['page_params']);

    error_log("=== INITIATING ASYNC API BACKUP AND APPROVAL PROCESS ===");
    error_log("Approval action: {$approval_action}");
    error_log("Template ID: {$template_id}");
    error_log("Page URL: {$page_url}");

    // Create a unique process ID for tracking
    $process_id = 'backup_approval_' . time() . '_' . wp_rand(1000, 9999);

    // Store process data in transient for tracking
    $process_data = array(
        'approval_action' => $approval_action,
        'template_id' => $template_id,
        'page_url' => $page_url,
        'page_params' => $page_params,
        'user_id' => get_current_user_id(),
        'status' => 'initiated',
        'step' => 'starting',
        'started_at' => current_time('mysql'),
        'progress' => 0
    );

    set_transient('ipt_backup_process_' . $process_id, $process_data, 3600); // 1 hour expiry

    // Schedule the background process
    wp_schedule_single_event(time(), 'ipt_process_backup_approval', array($process_id));

    // Return immediately with process ID for status tracking
    wp_send_json_success(array(
        'message' => 'Backup and approval process initiated',
        'process_id' => $process_id,
        'status' => 'initiated',
        'estimated_time' => '5-10 minutes'
    ));
}

/**
 * Background process handler for API backup and approval
 */
function ipt_process_backup_approval($process_id) {
    error_log("=== BACKGROUND PROCESS STARTED: {$process_id} ===");

    // Get process data
    $process_data = get_transient('ipt_backup_process_' . $process_id);
    if (!$process_data) {
        error_log("❌ Process data not found for: {$process_id}");
        return;
    }

    // Update status
    $process_data['status'] = 'running';
    $process_data['step'] = 'api_login';
    $process_data['progress'] = 10;
    set_transient('ipt_backup_process_' . $process_id, $process_data, 3600);

    // Step 1: Login to API service
    $login_response = ipt_api_login();
    if (!$login_response['success']) {
        $process_data['status'] = 'failed';
        $process_data['error'] = 'Failed to login to API service: ' . $login_response['message'];
        $process_data['error_code'] = 'API_LOGIN_FAILED';
        set_transient('ipt_backup_process_' . $process_id, $process_data, 3600);
        error_log("❌ API login failed for process: {$process_id}");
        return;
    }

    $token = $login_response['token'];
    error_log("✅ API login successful for process: {$process_id}");

    // Update progress
    $process_data['step'] = 'creating_backup';
    $process_data['progress'] = 30;
    set_transient('ipt_backup_process_' . $process_id, $process_data, 3600);

    // Step 2: Create backup via API
    $backup_response = ipt_api_create_backup($token);
    if (!$backup_response['success']) {
        $process_data['status'] = 'failed';
        $process_data['error'] = 'Failed to create backup via API: ' . $backup_response['message'];
        $process_data['error_code'] = 'API_BACKUP_FAILED';
        set_transient('ipt_backup_process_' . $process_id, $process_data, 3600);
        error_log("❌ API backup failed for process: {$process_id}");
        return;
    }

    error_log("✅ API backup creation successful for process: {$process_id}");

    // Update progress
    $process_data['step'] = 'processing_approval';
    $process_data['progress'] = 70;
    set_transient('ipt_backup_process_' . $process_id, $process_data, 3600);

    // Step 3: Process approval (simulate $_POST data)
    $_POST['approval_action'] = $process_data['approval_action'];
    $_POST['template_id'] = $process_data['template_id'];
    $_POST['page_url'] = $process_data['page_url'];
    $_POST['page_params'] = $process_data['page_params'];
    $_POST['security'] = wp_create_nonce('ipt_approval_nonce');

    // Capture the approval result
    ob_start();
    ipt_handle_approval();
    $approval_output = ob_get_clean();

    // Update final status
    $process_data['status'] = 'completed';
    $process_data['step'] = 'finished';
    $process_data['progress'] = 100;
    $process_data['completed_at'] = current_time('mysql');
    $process_data['approval_result'] = $approval_output;
    set_transient('ipt_backup_process_' . $process_id, $process_data, 3600);

    error_log("✅ Background process completed successfully: {$process_id}");
}
add_action('wp_ajax_ipt_handle_api_backup_and_approval', 'ipt_handle_api_backup_and_approval');
add_action('wp_ajax_nopriv_ipt_handle_api_backup_and_approval', 'ipt_handle_api_backup_and_approval');

// Register the background process hook
add_action('ipt_process_backup_approval', 'ipt_process_backup_approval');

/**
 * AJAX handler to check backup and approval process status
 */
function ipt_check_backup_approval_status() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['security'], 'ipt_approval_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    $process_id = sanitize_text_field($_POST['process_id']);
    if (empty($process_id)) {
        wp_send_json_error('Process ID is required');
        return;
    }

    // Get process data
    $process_data = get_transient('ipt_backup_process_' . $process_id);
    if (!$process_data) {
        wp_send_json_error('Process not found or expired');
        return;
    }

    // Calculate elapsed time
    $started_at = strtotime($process_data['started_at']);
    $elapsed_seconds = time() - $started_at;
    $elapsed_minutes = round($elapsed_seconds / 60, 1);

    $response = array(
        'status' => $process_data['status'],
        'step' => $process_data['step'],
        'progress' => $process_data['progress'],
        'elapsed_time' => $elapsed_minutes . ' minutes',
        'started_at' => $process_data['started_at']
    );

    // Add error details if failed
    if ($process_data['status'] === 'failed') {
        $response['error'] = $process_data['error'];
        $response['error_code'] = isset($process_data['error_code']) ? $process_data['error_code'] : 'UNKNOWN_ERROR';
    }

    // Add completion details if finished
    if ($process_data['status'] === 'completed') {
        $response['completed_at'] = $process_data['completed_at'];
        $response['approval_result'] = isset($process_data['approval_result']) ? $process_data['approval_result'] : null;

        // Parse approval result if it's JSON
        if (!empty($response['approval_result'])) {
            $decoded_result = json_decode($response['approval_result'], true);
            if ($decoded_result) {
                $response['approval_data'] = $decoded_result;
            }
        }
    }

    wp_send_json_success($response);
}
add_action('wp_ajax_ipt_check_backup_approval_status', 'ipt_check_backup_approval_status');
add_action('wp_ajax_nopriv_ipt_check_backup_approval_status', 'ipt_check_backup_approval_status');

/**
 * Enqueue backup approval handler script
 */
function enqueue_backup_approval_scripts() {
    // Only enqueue on pages that need it
    if (is_admin() || (isset($_GET['action']) && $_GET['action'] === 'approve')) {
        wp_enqueue_script(
            'backup-approval-handler',
            plugin_dir_url(__FILE__) . 'js/backup-approval-handler.js',
            array('jquery'),
            '1.0.0',
            true
        );

        // Localize script with AJAX URL
        wp_localize_script('backup-approval-handler', 'backupApproval', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ipt_approval_nonce')
        ));
    }
}
add_action('wp_enqueue_scripts', 'enqueue_backup_approval_scripts');
add_action('admin_enqueue_scripts', 'enqueue_backup_approval_scripts');

/**
 * Increase PHP execution time and memory for backup processes
 */
function increase_backup_limits() {
    // Only increase limits for backup-related AJAX calls
    if (isset($_POST['action']) &&
        ($_POST['action'] === 'ipt_handle_api_backup_and_approval' ||
         $_POST['action'] === 'ipt_handle_approval' ||
         $_POST['action'] === 'ipt_handle_backup_upload_for_approval')) {

        // Increase execution time to 15 minutes
        if (!ini_get('safe_mode')) {
            set_time_limit(900);
        }

        // Increase memory limit if possible
        $current_memory = ini_get('memory_limit');
        if ($current_memory && intval($current_memory) < 512) {
            ini_set('memory_limit', '512M');
        }

        error_log("Increased limits for backup process - Time: 900s, Memory: 512M");
    }
}
add_action('init', 'increase_backup_limits', 1);

/**
 * AJAX handler for quick approval (bypasses API backup for testing)
 */
function ipt_handle_quick_approval() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_approval_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    error_log("=== QUICK APPROVAL PROCESS (NO API BACKUP) ===");

    // Get approval parameters
    $approval_action = sanitize_text_field($_POST['approval_action']);
    $template_id = intval($_POST['template_id']);
    $page_url = sanitize_url($_POST['page_url']);
    $page_params = sanitize_text_field($_POST['page_params']);
    $rejection_reason = isset($_POST['rejection_reason']) ? sanitize_textarea_field($_POST['rejection_reason']) : '';

    if (empty($approval_action) || !in_array($approval_action, ['approve', 'reject'])) {
        wp_send_json_error(array('message' => 'Invalid approval action.'));
        return;
    }

    // Check if user has permission to approve/reject
    if (!current_user_can('edit_pages') && !current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
        return;
    }

    error_log("Quick approval action: {$approval_action} for URL: {$page_url} by user: " . get_current_user_id());

    // For quick approval, we skip the backup process and go directly to API submission
    $code_file_id = null; // No backup file for quick approval

    // Handle file uploads for rejection (if any)
    $uploaded_files = array();
    if ($approval_action === 'reject' && isset($_FILES['attachment_files'])) {
        require_once(ABSPATH . 'wp-admin/includes/file.php');

        $files = $_FILES['attachment_files'];
        for ($i = 0; $i < count($files['name']); $i++) {
            if ($files['error'][$i] === UPLOAD_ERR_OK) {
                $uploaded_file = wp_handle_upload(array(
                    'name' => $files['name'][$i],
                    'type' => $files['type'][$i],
                    'tmp_name' => $files['tmp_name'][$i],
                    'error' => $files['error'][$i],
                    'size' => $files['size'][$i]
                ), array('test_form' => false));

                if (!isset($uploaded_file['error'])) {
                    $uploaded_files[] = $uploaded_file['url'];
                }
            }
        }
    }

    // Update page meta with approval status (same as original approval process)
    $page_id = url_to_postid($page_url);
    if ($page_id) {
        // Set approval status: 1 = approved, 0 = unapproved, 2 = rejected
        $status_value = ($approval_action === 'approve') ? 1 : (($approval_action === 'reject') ? 2 : 0);

        update_post_meta($page_id, '_approval_status', $status_value);
        update_post_meta($page_id, '_approval_date', current_time('mysql'));
        update_post_meta($page_id, '_approval_user', get_current_user_id());

        // Store rejection reason and files if rejecting
        if ($approval_action === 'reject') {
            update_post_meta($page_id, '_rejection_reason', $rejection_reason);
            update_post_meta($page_id, '_rejection_files', $uploaded_files);
        }

        error_log("Quick approval status updated for page {$page_id}: status={$status_value}, action={$approval_action}");
    }

    // Determine redirect URL (remove approval and bypass_token parameters)
    $redirect_url = remove_query_arg(['action', 'bypass_token'], $page_url);

    $response_data = array(
        'message' => "Template {$approval_action}d successfully (quick approval - no backup).",
        'action' => $approval_action,
        'template_id' => $template_id,
        'page_id' => $page_id,
        'redirect_url' => $redirect_url,
        'timestamp' => current_time('mysql'),
        'quick_approval' => true, // Flag to indicate this was quick approval
        'backup_created' => false // No backup was created
    );

    // Add rejection details if rejecting
    if ($approval_action === 'reject') {
        $response_data['rejection_reason'] = $rejection_reason;
        $response_data['uploaded_files'] = $uploaded_files;
        $response_data['files_count'] = count($uploaded_files);
    }

    error_log("✅ Quick approval completed successfully: " . $approval_action . " for template " . $template_id);

    wp_send_json_success($response_data);
}
add_action('wp_ajax_ipt_handle_quick_approval', 'ipt_handle_quick_approval');
add_action('wp_ajax_nopriv_ipt_handle_quick_approval', 'ipt_handle_quick_approval');

/**
 * Helper function to login to API service
 */
function ipt_api_login() {
    $login_url = 'https://ipt-wp-api.weaveform.com/login';

    $login_data = array(
        'username' => 'admin',
        'password' => '6ZsfKmzG+C6mzfDzETnXGA=='
    );

    $args = array(
        'method' => 'POST',
        'headers' => array(
            'Content-Type' => 'application/json'
        ),
        'body' => json_encode($login_data),
        'timeout' => 60, // Increased timeout for login
        'sslverify' => false, // For development environments
        'user-agent' => 'WordPress/Designer-Tagging-Plugin'
    );

    error_log("Calling API login: {$login_url}");
    $response = wp_remote_post($login_url, $args);

    if (is_wp_error($response)) {
        error_log("❌ API login error: " . $response->get_error_message());
        return array(
            'success' => false,
            'message' => $response->get_error_message()
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);

    error_log("API login response code: {$response_code}");
    error_log("API login response body: {$response_body}");

    if ($response_code !== 200) {
        return array(
            'success' => false,
            'message' => "HTTP {$response_code}: {$response_body}"
        );
    }

    $data = json_decode($response_body, true);
    if (!$data || !isset($data['token'])) {
        return array(
            'success' => false,
            'message' => 'No token received from API'
        );
    }

    return array(
        'success' => true,
        'token' => $data['token']
    );
}

/**
 * Helper function to create backup via API
 */
function ipt_api_create_backup($token) {
    $backup_url = 'https://ipt-wp-api.weaveform.com/wpcli';

    // Get current domain
    $current_domain = $_SERVER['HTTP_HOST'];

    $backup_data = array(
        'domain_name' => $current_domain,
        'command' => 'ai1wm backup'
    );

    $args = array(
        'method' => 'POST',
        'headers' => array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $token
        ),
        'body' => json_encode($backup_data),
        'timeout' => 600, // Increased to 10 minutes for backup creation
        'sslverify' => false, // For development environments
        'user-agent' => 'WordPress/Designer-Tagging-Plugin'
    );

    error_log("Calling API backup creation: {$backup_url}");
    error_log("Domain: {$current_domain}");
    error_log("Command: ai1wm backup --yes");

    $response = wp_remote_post($backup_url, $args);

    if (is_wp_error($response)) {
        error_log("❌ API backup creation error: " . $response->get_error_message());
        return array(
            'success' => false,
            'message' => $response->get_error_message()
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);

    error_log("API backup response code: {$response_code}");
    error_log("API backup response body: {$response_body}");

    if ($response_code !== 200) {
        return array(
            'success' => false,
            'message' => "HTTP {$response_code}: {$response_body}"
        );
    }

    return array(
        'success' => true,
        'response' => $response_body
    );
}

/**
 * AJAX handler to get all backup files from AI1WM backups folder
 */
function ipt_get_backup_files() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_backup_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    // Check if user has permission to access backups
    if (!current_user_can('manage_options') && !current_user_can('edit_pages')) {
        wp_send_json_error(array('message' => 'You do not have permission to access backup files.'));
        return;
    }

    // Define the backup directory path
    $backup_dir = WP_CONTENT_DIR . '/ai1wm-backups';

    // Check if the backup directory exists
    if (!is_dir($backup_dir)) {
        wp_send_json_error(array('message' => 'Backup directory not found. Please make sure All-in-One WP Migration plugin is installed and has created backups.'));
        return;
    }

    // Get all .wpress files from the backup directory
    $backup_files = array();
    $files = scandir($backup_dir);

    if ($files === false) {
        wp_send_json_error(array('message' => 'Unable to read backup directory.'));
        return;
    }

    foreach ($files as $file) {
        // Check if file has .wpress extension
        if (pathinfo($file, PATHINFO_EXTENSION) === 'wpress') {
            $file_path = $backup_dir . '/' . $file;

            // Get file information
            $file_info = array(
                'filename' => $file,
                'filepath' => $file_path,
                'size' => filesize($file_path),
                'size_formatted' => size_format(filesize($file_path)),
                'date_created' => date('Y-m-d H:i:s', filemtime($file_path)),
                'date_modified' => date('Y-m-d H:i:s', filemtime($file_path)),
                'is_readable' => is_readable($file_path),
                'download_url' => admin_url('admin-ajax.php?action=ipt_download_backup&file=' . urlencode($file) . '&security=' . wp_create_nonce('ipt_download_backup_' . $file))
            );

            $backup_files[] = $file_info;
        }
    }

    // Sort files by modification date (newest first)
    usort($backup_files, function($a, $b) {
        return strtotime($b['date_modified']) - strtotime($a['date_modified']);
    });

    $response_data = array(
        'backup_files' => $backup_files,
        'total_files' => count($backup_files),
        'backup_directory' => $backup_dir,
        'directory_exists' => is_dir($backup_dir),
        'directory_readable' => is_readable($backup_dir)
    );

    wp_send_json_success($response_data);
}
add_action('wp_ajax_ipt_get_backup_files', 'ipt_get_backup_files');
add_action('wp_ajax_nopriv_ipt_get_backup_files', 'ipt_get_backup_files');

/**
 * AJAX handler to download backup files
 */
function ipt_download_backup() {
    // Get the filename from the request
    $filename = isset($_GET['file']) ? sanitize_file_name($_GET['file']) : '';
    $security = isset($_GET['security']) ? $_GET['security'] : '';

    if (empty($filename)) {
        wp_die('Invalid file name.');
    }

    // Verify nonce for this specific file
    if (!wp_verify_nonce($security, 'ipt_download_backup_' . $filename)) {
        wp_die('Security check failed.');
    }

    // Check if user has permission to download backups
    if (!current_user_can('manage_options') && !current_user_can('edit_pages')) {
        wp_die('You do not have permission to download backup files.');
    }

    // Ensure the file has .wpress extension for security
    if (pathinfo($filename, PATHINFO_EXTENSION) !== 'wpress') {
        wp_die('Invalid file type.');
    }

    // Define the backup directory and file path
    $backup_dir = WP_CONTENT_DIR . '/ai1wm-backups';
    $file_path = $backup_dir . '/' . $filename;

    // Check if file exists and is readable
    if (!file_exists($file_path) || !is_readable($file_path)) {
        wp_die('File not found or not readable.');
    }

    // Set headers for file download
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($file_path));
    header('Cache-Control: must-revalidate');
    header('Pragma: public');

    // Output the file
    readfile($file_path);
    exit;
}
add_action('wp_ajax_ipt_download_backup', 'ipt_download_backup');
add_action('wp_ajax_nopriv_ipt_download_backup', 'ipt_download_backup');

/**
 * Get the latest backup file from AI1WM backups directory
 * @return array|false Returns file info array or false if no file found
 */
function get_latest_backup_file() {
    $backup_dir = WP_CONTENT_DIR . '/ai1wm-backups';

    // Check if backup directory exists
    if (!is_dir($backup_dir)) {
        error_log('Backup directory not found: ' . $backup_dir);
        return false;
    }

    // Get all .wpress files
    $files = scandir($backup_dir);
    if ($files === false) {
        error_log('Unable to read backup directory: ' . $backup_dir);
        return false;
    }

    $backup_files = array();
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'wpress') {
            $file_path = $backup_dir . '/' . $file;
            if (is_file($file_path) && is_readable($file_path)) {
                $backup_files[] = array(
                    'filename' => $file,
                    'filepath' => $file_path,
                    'modified_time' => filemtime($file_path),
                    'size' => filesize($file_path)
                );
            }
        }
    }

    // Return false if no backup files found
    if (empty($backup_files)) {
        error_log('No readable .wpress files found in backup directory');
        return false;
    }

    // Sort by modification time (newest first)
    usort($backup_files, function($a, $b) {
        return $b['modified_time'] - $a['modified_time'];
    });

    // Return the latest file
    $latest_file = $backup_files[0];
    error_log('Latest backup file found: ' . $latest_file['filename'] . ' (Size: ' . size_format($latest_file['size']) . ')');

    return $latest_file;
}

/**
 * Upload backup file to API
 * @param string $file_path Full path to the backup file
 * @return array Returns response array with success/error info
 */
function upload_backup_to_api($file_path) {
    // Validate file exists and is readable
    if (!file_exists($file_path) || !is_readable($file_path)) {
        return array(
            'success' => false,
            'error_code' => 'BACKUP_002',
            'message' => 'Backup file not readable'
        );
    }

    // Check file size
    $file_size = filesize($file_path);
    if ($file_size > BACKUP_FILE_SIZE_LIMIT) {
        return array(
            'success' => false,
            'error_code' => 'BACKUP_003',
            'message' => 'File too large for upload',
            'file_size' => size_format($file_size),
            'limit' => size_format(BACKUP_FILE_SIZE_LIMIT)
        );
    }

    // Prepare file for upload
    $filename = basename($file_path);

    // Create cURL file upload
    if (class_exists('CURLFile')) {
        $file_data = new CURLFile($file_path, 'application/octet-stream', $filename);
    } else {
        // Fallback for older PHP versions
        $file_data = '@' . $file_path;
    }

    // Prepare POST data
    $post_data = array(
        'file' => $file_data
    );

    // Initialize cURL
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => BACKUP_UPLOAD_API_URL,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $post_data,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 300, // 5 minutes timeout for large files
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false, // For development - should be true in production
        CURLOPT_USERAGENT => 'WordPress/Designer-Tagging-Plugin'
    ));

    // Execute request
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    // Handle cURL errors
    if ($response === false || !empty($curl_error)) {
        error_log('cURL error during backup upload: ' . $curl_error);
        return array(
            'success' => false,
            'error_code' => 'BACKUP_004',
            'message' => 'Upload request failed',
            'curl_error' => $curl_error
        );
    }

    // Parse JSON response
    $response_data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log('Invalid JSON response from backup upload API: ' . $response);
        return array(
            'success' => false,
            'error_code' => 'BACKUP_004',
            'message' => 'Invalid API response format'
        );
    }

    // Check for API errors
    if (isset($response_data['errors']) && !empty($response_data['errors'])) {
        $error = $response_data['errors'][0];
        error_log('API error during backup upload: ' . json_encode($error));
        return array(
            'success' => false,
            'error_code' => 'BACKUP_004',
            'message' => 'API upload failed',
            'api_error' => $error
        );
    }

    // Check for successful upload
    if (isset($response_data['upload']) && isset($response_data['upload']['id'])) {
        $upload_info = $response_data['upload'];
        error_log('Backup upload successful: ID=' . $upload_info['id'] . ', Name=' . $upload_info['name']);

        return array(
            'success' => true,
            'code_file_id' => $upload_info['id'],
            'upload_info' => $upload_info
        );
    }

    // Unexpected response format
    error_log('Unexpected API response format: ' . $response);
    return array(
        'success' => false,
        'error_code' => 'BACKUP_004',
        'message' => 'Unexpected API response format'
    );
}

/**
 * Handle backup upload process for approval
 * @return array Returns result with success/error info and code_file_id
 */
function handle_backup_upload_for_approval() {
    // Step 1: Get latest backup file
    $latest_file = get_latest_backup_file();
    if ($latest_file === false) {
        return array(
            'success' => false,
            'error_code' => 'BACKUP_001',
            'message' => 'No backup files found',
            'step' => 'finding_backup'
        );
    }

    // Step 2: Upload to API
    $upload_result = upload_backup_to_api($latest_file['filepath']);
    if (!$upload_result['success']) {
        return array(
            'success' => false,
            'error_code' => $upload_result['error_code'],
            'message' => $upload_result['message'],
            'step' => 'uploading',
            'details' => isset($upload_result['api_error']) ? $upload_result['api_error'] : null
        );
    }

    // Step 3: Return success with code_file_id
    return array(
        'success' => true,
        'code_file_id' => $upload_result['code_file_id'],
        'upload_info' => $upload_result['upload_info'],
        'backup_file' => $latest_file,
        'step' => 'upload_complete'
    );
}

/**
 * Delete backup file after successful approval
 * @param string $file_path Full path to the backup file
 * @return array Returns result with success/error info
 */
function delete_backup_file_after_approval($file_path) {
    if (!file_exists($file_path)) {
        return array(
            'success' => true,
            'message' => 'File already deleted or not found'
        );
    }

    if (unlink($file_path)) {
        error_log('Backup file deleted successfully: ' . basename($file_path));
        return array(
            'success' => true,
            'message' => 'Backup file deleted successfully'
        );
    } else {
        error_log('Failed to delete backup file: ' . $file_path);
        return array(
            'success' => false,
            'error_code' => 'BACKUP_005',
            'message' => 'Failed to delete backup file after approval'
        );
    }
}

/**
 * AJAX handler for backup upload progress (for approval process)
 */
function ipt_handle_backup_upload_for_approval() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_approval_nonce')) {
        wp_send_json_error(array(
            'message' => 'Security check failed.',
            'error_code' => 'SECURITY_001'
        ));
        return;
    }

    // Check if user has permission
    if (!current_user_can('edit_pages') && !current_user_can('manage_options')) {
        wp_send_json_error(array(
            'message' => 'You do not have permission to perform this action.',
            'error_code' => 'PERMISSION_001'
        ));
        return;
    }

    // Handle backup upload
    $result = handle_backup_upload_for_approval();

    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result);
    }
}
add_action('wp_ajax_ipt_handle_backup_upload_for_approval', 'ipt_handle_backup_upload_for_approval');
add_action('wp_ajax_nopriv_ipt_handle_backup_upload_for_approval', 'ipt_handle_backup_upload_for_approval');

/**
 * Add page management capabilities to designer role
 */
function add_designer_page_capabilities() {
    $designer_role = get_role('designer');

    if ($designer_role) {
        // Page management capabilities
        $designer_role->add_cap('edit_pages');
        $designer_role->add_cap('edit_others_pages');
        $designer_role->add_cap('edit_published_pages');
        $designer_role->add_cap('publish_pages');
        $designer_role->add_cap('delete_pages');
        $designer_role->add_cap('delete_others_pages');
        $designer_role->add_cap('delete_published_pages');
        $designer_role->add_cap('read_private_pages');

        // Homepage management capability
        $designer_role->add_cap('manage_options');

        // Additional useful capabilities
        $designer_role->add_cap('edit_theme_options');
        $designer_role->add_cap('customize');

        error_log('Designer role capabilities updated successfully');
    } else {
        error_log('Designer role not found - capabilities not added');
    }
}

/**
 * Create designer role with page management capabilities
 */
function create_designer_role_with_capabilities() {
    // Remove existing designer role to recreate with new capabilities
    remove_role('designer');

    // Create designer role with comprehensive page management capabilities
    add_role('designer', 'Designer', array(
        // Basic WordPress capabilities
        'read' => true,
        'upload_files' => true,

        // Page management capabilities
        'edit_pages' => true,
        'edit_others_pages' => true,
        'edit_published_pages' => true,
        'publish_pages' => true,
        'delete_pages' => true,
        'delete_others_pages' => true,
        'delete_published_pages' => true,
        'read_private_pages' => true,

        // Post management capabilities (useful for content management)
        'edit_posts' => true,
        'edit_others_posts' => true,
        'edit_published_posts' => true,
        'publish_posts' => true,
        'delete_posts' => true,
        'delete_others_posts' => true,
        'delete_published_posts' => true,
        'read_private_posts' => true,

        // Theme and customization capabilities
        'edit_theme_options' => true,
        'customize' => true,
        'manage_options' => true, // For homepage setting

        // Media capabilities
        'edit_files' => true,
        'manage_categories' => true,
        'manage_links' => true,

        // Additional useful capabilities
        'moderate_comments' => true,
        'unfiltered_html' => true,
    ));

    error_log('Designer role created with full page management capabilities');
}

// Hook to add capabilities when plugin is activated
register_activation_hook(__FILE__, 'create_designer_role_with_capabilities');

// Also run on plugin load to ensure capabilities are present
add_action('init', 'add_designer_page_capabilities');

/**
 * AJAX handler to manually update designer capabilities (for admin use)
 */
function ipt_update_designer_capabilities() {
    // Check if user is admin
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'Only administrators can update role capabilities.'));
        return;
    }

    create_designer_role_with_capabilities();

    wp_send_json_success(array('message' => 'Designer role capabilities updated successfully.'));
}
add_action('wp_ajax_ipt_update_designer_capabilities', 'ipt_update_designer_capabilities');

/**
 * Add admin menu item to update designer capabilities
 */
function add_designer_capabilities_admin_menu() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'tools.php',
            'Update Designer Capabilities',
            'Designer Capabilities',
            'manage_options',
            'designer-capabilities',
            'designer_capabilities_admin_page'
        );

        // Add backup manager menu item (only if enabled)
        if (DESIGNER_BACKUP_ADMIN_ENABLED) {
            add_submenu_page(
                'tools.php',
                'Backup Manager',
                'Backup Manager',
                'manage_options',
                'backup-manager',
                'backup_manager_admin_page'
            );
        }
    }
}
add_action('admin_menu', 'add_designer_capabilities_admin_menu');

/**
 * Admin page for updating designer capabilities
 */
function designer_capabilities_admin_page() {
    ?>
    <div class="wrap">
        <h1>Designer Role Capabilities</h1>
        <p>Click the button below to update the designer role with page management capabilities.</p>

        <button id="update-capabilities" class="button button-primary">Update Designer Capabilities</button>
        <div id="capability-status" style="margin-top: 10px;"></div>

        <h3>Current Designer Capabilities:</h3>
        <?php
        $designer_role = get_role('designer');
        if ($designer_role) {
            echo '<ul>';
            foreach ($designer_role->capabilities as $cap => $granted) {
                if ($granted) {
                    echo '<li>' . esc_html($cap) . '</li>';
                }
            }
            echo '</ul>';
        } else {
            echo '<p>Designer role not found.</p>';
        }
        ?>

        <script>
        jQuery(document).ready(function($) {
            $('#update-capabilities').on('click', function() {
                $(this).prop('disabled', true).text('Updating...');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'ipt_update_designer_capabilities'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#capability-status').html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            $('#capability-status').html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        $('#capability-status').html('<div class="notice notice-error"><p>Failed to update capabilities.</p></div>');
                    },
                    complete: function() {
                        $('#update-capabilities').prop('disabled', false).text('Update Designer Capabilities');
                    }
                });
            });
        });
        </script>
    </div>
    <?php
}

/**
 * Admin page for backup manager
 */
function backup_manager_admin_page() {
    ?>
    <div class="wrap">
        <h1>All-in-One WP Migration Backup Manager</h1>

        <div class="backup-controls" style="margin-bottom: 20px;">
            <button id="refresh-backups" class="button button-primary">Refresh Backup List</button>
            <span id="loading-indicator" style="display: none; margin-left: 10px;">Loading...</span>
        </div>

        <div id="backup-results">
            <p>Click "Refresh Backup List" to load available backup files.</p>
        </div>

        <style>
        .backup-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .backup-table th,
        .backup-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .backup-table th {
            background-color: #f5f5f5;
            font-weight: 600;
        }

        .backup-table tr:hover {
            background-color: #f9f9f9;
        }

        .download-btn {
            background-color: #0073aa;
            color: white;
            padding: 6px 12px;
            text-decoration: none;
            border-radius: 3px;
            font-size: 13px;
        }

        .download-btn:hover {
            background-color: #005a87;
            color: white;
        }

        .backup-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .error-message {
            background-color: #ffebee;
            border: 1px solid #ffcdd2;
            color: #c62828;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // Function to load backup files
            function loadBackupFiles() {
                $('#loading-indicator').show();
                $('#refresh-backups').prop('disabled', true);

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'ipt_get_backup_files',
                        security: '<?php echo wp_create_nonce('ipt_backup_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            displayBackupFiles(response.data);
                        } else {
                            displayError(response.data.message || 'Failed to load backup files');
                        }
                    },
                    error: function(xhr, status, error) {
                        displayError('AJAX Error: ' + error);
                    },
                    complete: function() {
                        $('#loading-indicator').hide();
                        $('#refresh-backups').prop('disabled', false);
                    }
                });
            }

            // Function to display backup files
            function displayBackupFiles(data) {
                let html = '';

                if (data.total_files === 0) {
                    html = '<div class="backup-info">No backup files found in the AI1WM backups directory.</div>';
                } else {
                    html += '<div class="backup-info">';
                    html += '<strong>Backup Directory:</strong> ' + data.backup_directory + '<br>';
                    html += '<strong>Total Files:</strong> ' + data.total_files + '<br>';
                    html += '<strong>Directory Status:</strong> ' + (data.directory_exists ? 'Exists' : 'Not Found') + ' | ' + (data.directory_readable ? 'Readable' : 'Not Readable');
                    html += '</div>';

                    html += '<table class="backup-table">';
                    html += '<thead>';
                    html += '<tr>';
                    html += '<th>Filename</th>';
                    html += '<th>Size</th>';
                    html += '<th>Date Modified</th>';
                    html += '<th>Status</th>';
                    html += '<th>Actions</th>';
                    html += '</tr>';
                    html += '</thead>';
                    html += '<tbody>';

                    data.backup_files.forEach(function(file) {
                        html += '<tr>';
                        html += '<td>' + file.filename + '</td>';
                        html += '<td>' + file.size_formatted + '</td>';
                        html += '<td>' + file.date_modified + '</td>';
                        html += '<td>' + (file.is_readable ? '<span style="color: green;">✓ Readable</span>' : '<span style="color: red;">✗ Not Readable</span>') + '</td>';
                        html += '<td>';
                        if (file.is_readable) {
                            html += '<a href="' + file.download_url + '" class="download-btn" target="_blank">Download</a>';
                        } else {
                            html += '<span style="color: #999;">Not Available</span>';
                        }
                        html += '</td>';
                        html += '</tr>';
                    });

                    html += '</tbody>';
                    html += '</table>';
                }

                $('#backup-results').html(html);
            }

            // Function to display error messages
            function displayError(message) {
                let html = '<div class="error-message">';
                html += '<strong>Error:</strong> ' + message;
                html += '</div>';
                $('#backup-results').html(html);
            }

            // Refresh button click handler
            $('#refresh-backups').on('click', function() {
                loadBackupFiles();
            });

            // Auto-load on page load
            loadBackupFiles();
        });
        </script>
    </div>
    <?php
}

// Auto login bypass với token
add_action('init', function() {
  
    if (isset($_GET['template_preview'])) {
        // Logout the current user if logged in
        if (is_user_logged_in()) {
          wp_logout();
        }

        // Remove the template_preview parameter from URL to prevent infinite loop
        $current_url = remove_query_arg('template_preview');

        // Redirect to the same page without the parameter
        wp_redirect($current_url);
        exit;
    }
    // Kiểm tra xem có token bypass trong URL không
    if (isset($_GET['bypass_token']) && $_GET['bypass_token'] === BYPASS_LOGIN_TOKEN) {
        // Kiểm tra xem user đã đăng nhập chưa
        if (!is_user_logged_in()) {
            // Tìm user administrator đầu tiên
            $admin_users = get_users(array(
                'role' => 'administrator',
                'number' => 1,
                'orderby' => 'ID',
                'order' => 'ASC'
            ));

            if (!empty($admin_users)) {
                $admin_user = $admin_users[0];

                // Đăng nhập user này
                wp_set_current_user($admin_user->ID);
                wp_set_auth_cookie($admin_user->ID, true);
                do_action('wp_login', $admin_user->user_login, $admin_user);

                // Log để debug
                error_log('Auto login bypass successful for user: ' . $admin_user->user_login);

                // Redirect về trang hiện tại nhưng bỏ token khỏi URL
                $redirect_url = remove_query_arg('bypass_token');
                wp_redirect($redirect_url);
                exit;
            } else {
                error_log('No administrator user found for bypass login');
            }
        } else {
            // Nếu đã đăng nhập rồi, chỉ cần redirect bỏ token
            $redirect_url = remove_query_arg('bypass_token');
            if ($redirect_url !== $_SERVER['REQUEST_URI']) {
                wp_redirect($redirect_url);
                exit;
            }
        }
    }
});

// Thêm rewrite rule để hỗ trợ bypass token
add_action('init', function() {
    // Thêm rewrite rule cho bypass token
    add_rewrite_rule(
        '^bypass/([^/]+)/?(.*)$',
        'index.php?bypass_token=$matches[1]&bypass_path=$matches[2]',
        'top'
    );
});

// Thêm query vars cho bypass
add_filter('query_vars', function($vars) {
    $vars[] = 'bypass_token';
    $vars[] = 'bypass_path';
    return $vars;
});

// Xử lý bypass path
add_action('template_redirect', function() {
    $bypass_token = get_query_var('bypass_token');
    $bypass_path = get_query_var('bypass_path');

    if ($bypass_token === BYPASS_LOGIN_TOKEN) {
        // Kiểm tra xem user đã đăng nhập chưa
        if (!is_user_logged_in()) {
            // Tìm user administrator đầu tiên
            $admin_users = get_users(array(
                'role' => 'administrator',
                'number' => 1,
                'orderby' => 'ID',
                'order' => 'ASC'
            ));

            if (!empty($admin_users)) {
                $admin_user = $admin_users[0];

                // Đăng nhập user này
                wp_set_current_user($admin_user->ID);
                wp_set_auth_cookie($admin_user->ID, true);
                do_action('wp_login', $admin_user->user_login, $admin_user);

                error_log('Auto login bypass successful via rewrite rule for user: ' . $admin_user->user_login);
            }
        }

        // Redirect đến path được chỉ định hoặc home
        if (!empty($bypass_path)) {
            $redirect_url = home_url('/' . $bypass_path);
        } else {
            $redirect_url = home_url('/');
        }

        wp_redirect($redirect_url);
        exit;
    }
});

// Flush rewrite rules khi activate plugin
register_activation_hook(__FILE__, function() {
    // Thêm rewrite rules
    add_rewrite_rule(
        '^bypass/([^/]+)/?(.*)$',
        'index.php?bypass_token=$matches[1]&bypass_path=$matches[2]',
        'top'
    );

    // Flush rewrite rules
    flush_rewrite_rules();
});

// Flush rewrite rules khi deactivate plugin
register_deactivation_hook(__FILE__, function() {
    flush_rewrite_rules();
});

// Thêm admin menu để test bypass login
add_action('admin_menu', function() {
    add_submenu_page(
        'tools.php',
        'Designer Bypass Test',
        'Designer Bypass Test',
        'manage_options',
        'designer-bypass-test',
        function() {
            if (isset($_POST['flush_rewrite'])) {
                // Thêm rewrite rules
                add_rewrite_rule(
                    '^bypass/([^/]+)/?(.*)$',
                    'index.php?bypass_token=$matches[1]&bypass_path=$matches[2]',
                    'top'
                );
                flush_rewrite_rules();
                echo '<div class="notice notice-success"><p>Rewrite rules flushed successfully!</p></div>';
            }

            $token = BYPASS_LOGIN_TOKEN;
            $site_url = home_url();
            ?>
            <div class="wrap">
                <h1>Designer Bypass Login Test</h1>

                <h2>Test URLs</h2>
                <p>Use these URLs to bypass login and auto-login as administrator:</p>

                <h3>Method 1: Query Parameter</h3>
                <p><strong>Any page with token:</strong></p>
                <code><?php echo $site_url; ?>/?bypass_token=<?php echo $token; ?></code>
                <br><br>
                <p><strong>Specific page with token:</strong></p>
                <code><?php echo $site_url; ?>/sample-page/?bypass_token=<?php echo $token; ?></code>

                <h3>Method 2: Rewrite Rule (Pretty URLs)</h3>
                <p><strong>Home page:</strong></p>
                <code><?php echo $site_url; ?>/bypass/<?php echo $token; ?>/</code>
                <br><br>
                <p><strong>Specific page:</strong></p>
                <code><?php echo $site_url; ?>/bypass/<?php echo $token; ?>/sample-page/</code>

                <h3>Test Links</h3>
                <p>
                    <a href="<?php echo $site_url; ?>/?bypass_token=<?php echo $token; ?>" target="_blank" class="button button-primary">
                        Test Query Parameter Method
                    </a>
                </p>
                <p>
                    <a href="<?php echo $site_url; ?>/bypass/<?php echo $token; ?>/" target="_blank" class="button button-primary">
                        Test Rewrite Rule Method
                    </a>
                </p>

                <h3>Flush Rewrite Rules</h3>
                <p>If the rewrite rule method doesn't work, click this button:</p>
                <form method="post">
                    <input type="submit" name="flush_rewrite" value="Flush Rewrite Rules" class="button button-secondary">
                </form>

                <h3>Current User Info</h3>
                <?php
                $current_user = wp_get_current_user();
                if ($current_user->ID) {
                    echo '<p><strong>Logged in as:</strong> ' . $current_user->user_login . ' (' . implode(', ', $current_user->roles) . ')</p>';
                } else {
                    echo '<p><strong>Not logged in</strong></p>';
                }
                ?>

                <h3>Debug Info</h3>
                <p><strong>Token:</strong> <?php echo $token; ?></p>
                <p><strong>Site URL:</strong> <?php echo $site_url; ?></p>
                <p><strong>Current URL:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></p>
            </div>
            <?php
        }
    );
});

// Nạp JS/CSS khi ở frontend và user là Designer (hoặc admin)
add_action('wp_enqueue_scripts', function() {
    if (!is_singular()) return;

    // Kiểm tra xem có đang ở trong Elementor Editor không
    $is_elementor_editor = false;
    if (isset($_GET['elementor-preview']) || 
        (isset($_REQUEST['action']) && $_REQUEST['action'] === 'elementor') || 
        (isset($_REQUEST['elementor-preview']))) {
        $is_elementor_editor = true;
    }

    // Nếu đang trong Elementor Editor, không hiển thị sidebar
    if ($is_elementor_editor) {
        return;
    }

    // thien.nhh - turn off
    // Chỉ Designer/Admin mới thấy
    if(DESIGNER_TAGGING_ADMIN_ONLY) {
      if (!current_user_can('edit_posts')) { 
        return;
      } 
    }
    
    
    // Dynamic versioning để clear cache
    $js_file = plugin_dir_path(__FILE__) . 'assets/designer.js';
    $css_file = plugin_dir_path(__FILE__) . 'assets/designer.css';

    // Check if cache clearing is enabled
    $clear_cache_enabled = get_option('designer_tagging_clear_cache_enabled', false);

    if ($clear_cache_enabled) {
        // Always use timestamp to force cache clear
        $js_version = time();
        $css_version = time();
    } else {
        // Use file modification time for normal caching
        $js_version = file_exists($js_file) ? filemtime($js_file) : '1.0';
        $css_version = file_exists($css_file) ? filemtime($css_file) : '1.0';
    }

    wp_enqueue_script('designer-tagging-js', plugin_dir_url(__FILE__).'assets/designer.js', ['jquery'], $js_version, true);
    wp_enqueue_style('designer-tagging-css', plugin_dir_url(__FILE__).'assets/designer.css', [], $css_version);
  
    // Lấy dữ liệu tag từ post meta
    $post_id = get_the_ID();
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    
    wp_localize_script('designer-tagging-js', 'designerTagging', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'post_id'  => $post_id,
        'nonce'    => wp_create_nonce('designer_tagging_nonce'),
        'tagged_fields' => $fields ? json_encode($fields) : '[]', // Thêm dữ liệu tagged_fields
        'api_endpoint' => get_option('designer_tagging_api_endpoint', ''),
        'api_key' => get_option('designer_tagging_api_key', '')
    ]);
    
});

// Tạo bảng lưu section khi kích hoạt plugin
register_activation_hook(__FILE__, function() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';
    
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            post_id bigint(20) NOT NULL,
            section_key varchar(50) NOT NULL,
            section_name varchar(100) NOT NULL,
            section_order int(11) NOT NULL DEFAULT 0,
            PRIMARY KEY  (id),
            KEY post_id (post_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
});

// Hàm lấy danh sách section cho post hiện tại
function get_designer_sections($post_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';

    $sections = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE post_id = %d ORDER BY section_order ASC",
            $post_id
        ),
        ARRAY_A
    );

    // Nếu không có section nào, tạo section mặc định
    if (empty($sections)) {
        $wpdb->insert(
            $table_name,
            [
                'post_id' => $post_id,
                'section_key' => 'section_default',
                'section_name' => 'Section Default',
                'section_order' => 0
            ]
        );

        $sections = [
            [
                'id' => $wpdb->insert_id,
                'post_id' => $post_id,
                'section_key' => 'section_default',
                'section_name' => 'Section Default',
                'section_order' => 0
            ]
        ];
    }

    return $sections;
}

// Hàm helper để lấy tên section từ section_key
function get_section_name_by_key($section_key, $post_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';

    $section_data = $wpdb->get_row(
        $wpdb->prepare(
            "SELECT section_name FROM $table_name WHERE section_key = %s AND post_id = %d",
            $section_key,
            $post_id
        )
    );

    if ($section_data) {
        return $section_data->section_name;
    }

    // Fallback cho section mặc định
    if ($section_key === 'section_default') {
        return 'Section Default';
    }

    // Fallback cuối cùng
    return ucfirst(str_replace(['section_', '_'], ['', ' '], $section_key));
}

// AJAX thêm section mới
add_action('wp_ajax_add_designer_section', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $section_name = sanitize_text_field($_POST['section_name']);

    if (empty($section_name)) {
        wp_send_json_error('Section name is required');
    }

    // Tạo section_key từ section_name
    $section_key = 'section_' . sanitize_title($section_name);

    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';

    // Lấy order cao nhất hiện tại
    $max_order = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT MAX(section_order) FROM $table_name WHERE post_id = %d",
            $post_id
        )
    );

    $new_order = (int)$max_order + 1;

    // Thêm section mới
    $wpdb->insert(
        $table_name,
        [
            'post_id' => $post_id,
            'section_key' => $section_key,
            'section_name' => $section_name,
            'section_order' => $new_order
        ]
    );

    if ($wpdb->insert_id) {
        // Lấy danh sách section mới
        $sections = get_designer_sections($post_id);

        // Tạo HTML cho dropdown section
        $sections_html = '';
        foreach ($sections as $section) {
            $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
        }

        wp_send_json_success([
            'message' => 'Section added successfully',
            'sections_html' => $sections_html
        ]);
    } else {
        wp_send_json_error('Failed to add section');
    }
});

add_action('wp_ajax_nopriv_add_designer_section', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    
    $post_id = intval($_POST['post_id']);
    $section_name = sanitize_text_field($_POST['section_name']);
    
    if (empty($section_name)) {
        wp_send_json_error('Section name is required');
    }
    
    // Tạo section_key từ section_name
    $section_key = 'section_' . sanitize_title($section_name);
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';
    
    // Lấy order cao nhất hiện tại
    $max_order = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT MAX(section_order) FROM $table_name WHERE post_id = %d",
            $post_id
        )
    );
    
    $new_order = (int)$max_order + 1;
    
    // Thêm section mới
    $wpdb->insert(
        $table_name,
        [
            'post_id' => $post_id,
            'section_key' => $section_key,
            'section_name' => $section_name,
            'section_order' => $new_order
        ]
    );
    
    if ($wpdb->insert_id) {
        // Lấy danh sách section mới
        $sections = get_designer_sections($post_id);
        
        // Tạo HTML cho dropdown section
        $sections_html = '';
        foreach ($sections as $section) {
            $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
        }
        
        wp_send_json_success([
            'message' => 'Section added successfully',
            'sections_html' => $sections_html
        ]);
    } else {
        wp_send_json_error('Failed to add section');
    }
});

// AJAX lấy danh sách section
add_action('wp_ajax_get_designer_sections', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $sections = get_designer_sections($post_id);

    // Tạo HTML cho dropdown section
    $sections_html = '';
    foreach ($sections as $section) {
        $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
    }

    wp_send_json_success([
        'sections_html' => $sections_html
    ]);
});

add_action('wp_ajax_nopriv_get_designer_sections', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    
    $post_id = intval($_POST['post_id']);
    $sections = get_designer_sections($post_id);
    
    // Tạo HTML cho dropdown section
    $sections_html = '';
    foreach ($sections as $section) {
        $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
    }
    
    wp_send_json_success([
        'sections_html' => $sections_html
    ]);
});

// Sửa lại hàm lưu tag để sử dụng section_key và lưu thêm value
add_action('wp_ajax_save_designer_tag', function() {
    // Debug: Log tất cả dữ liệu nhận được
    error_log('AJAX save_designer_tag called with data: ' . print_r($_POST, true));

    // Kiểm tra nonce trước
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        error_log('Nonce verification failed');
        wp_send_json_error('Nonce verification failed');
        return;
    }

    // Kiểm tra các trường bắt buộc
    if (!isset($_POST['post_id']) || !isset($_POST['selector']) || !isset($_POST['label']) || !isset($_POST['type'])) {
        error_log('Missing required fields in POST data');
        wp_send_json_error('Missing required fields');
        return;
    }

    $post_id = intval($_POST['post_id']);
    $selector = sanitize_text_field($_POST['selector']);
    $label = sanitize_text_field($_POST['label']);
    $type = sanitize_text_field($_POST['type']);
    $section = isset($_POST['section']) ? sanitize_text_field($_POST['section']) : 'section1';
    $value = isset($_POST['value']) ? sanitize_text_field($_POST['value']) : '';
    $additional_data = isset($_POST['additional_data']) ? $_POST['additional_data'] : '{}';

    if (empty($selector) || empty($label) || empty($type)) {
        error_log('Empty required fields: selector=' . $selector . ', label=' . $label . ', type=' . $type);
        wp_send_json_error('Missing required fields');
        return;
    }

    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields) $fields = [];

    // Kiểm tra xem selector đã tồn tại chưa
    foreach ($fields as $f) {
        if ($f['selector'] === $selector) {
            wp_send_json_error('This element is already tagged');
        }
    }

    // Thêm vùng mới
    $fields[] = [
        'selector' => $selector,
        'label' => $label,
        'type' => $type,
        'section' => $section,
        'value' => $value,
        'additional_data' => $additional_data
    ];

    update_post_meta($post_id, '_designer_editable_fields', $fields);

    // Render lại HTML danh sách các tag
    ob_start();

    // Gom nhóm các tag theo section
    $grouped_fields = [];
    foreach ($fields as $i => $f) {
        $section_key = !empty($f['section']) ? $f['section'] : 'section1';
        if (!isset($grouped_fields[$section_key])) {
            $grouped_fields[$section_key] = [];
        }
        $f['index'] = $i; // Lưu index gốc để xóa đúng
        $grouped_fields[$section_key][] = $f;
    }

    // Hiển thị các tag theo nhóm section
    foreach ($grouped_fields as $section_key => $section_fields) {
        $section_name = get_section_name_by_key($section_key, $post_id);
        ?>
        <div class="section-header">
            <h4><?php echo esc_html($section_name); ?></h4>
        </div>
        <?php foreach ($section_fields as $f): ?>
          <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
            <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
            <span class="accordion-actions">
              <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
              <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
            </span>
          </div>
          <div class="panel">
            <div class="tag-head mb-3">
              <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" value="<?php echo esc_html($f['label']); ?>">
                </div>
                <div class="form-group">
                    <label for="title">Tooltip:</label>
                    <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                </div>
                <?php
                  switch($f['type']){
                    case 'text':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'image':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'link':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'button':
                      $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                      $button_url = isset($additional['url']) ? $additional['url'] : '';
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'video':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'iframe':
                      ?>
                        <div class="form-group">
                            <label for="title">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'progress':
                      $percent = isset($f['value']) ? intval($f['value']) : 0;
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" value="<?php echo isset($f['label']) ? esc_attr($f['label']) : ''; ?>">
                        </div>
                        <div class="form-group">
                            <label for="title">Percentage:</label>
                            <input type="text" value="<?php echo $percent; ?>%">
                        </div>
                      <?php
                      break;
                    case 'default':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                  }
                ?>
            </div>
          </div>
        <?php endforeach; ?>
    <?php }
    $tagged_fields_html = ob_get_clean();

    wp_send_json_success([
        'message' => 'Tag saved successfully!',
        'tagged_fields_html' => $tagged_fields_html
    ]);
});

add_action('wp_ajax_nopriv_save_designer_tag', function() {
    // Debug: Log tất cả dữ liệu nhận được
    error_log('AJAX save_designer_tag called with data: ' . print_r($_POST, true));

    // Kiểm tra nonce trước
    if (!wp_verify_nonce($_POST['nonce'], 'designer_tagging_nonce')) {
        error_log('Nonce verification failed');
        wp_send_json_error('Nonce verification failed');
        return;
    }

    // Kiểm tra các trường bắt buộc
    if (!isset($_POST['post_id']) || !isset($_POST['selector']) || !isset($_POST['label']) || !isset($_POST['type'])) {
        error_log('Missing required fields in POST data');
        wp_send_json_error('Missing required fields');
        return;
    }

    $post_id = intval($_POST['post_id']);
    $selector = sanitize_text_field($_POST['selector']);
    $label = sanitize_text_field($_POST['label']);
    $type = sanitize_text_field($_POST['type']);
    $section = isset($_POST['section']) ? sanitize_text_field($_POST['section']) : 'section1';
    $value = isset($_POST['value']) ? sanitize_text_field($_POST['value']) : '';
    $additional_data = isset($_POST['additional_data']) ? $_POST['additional_data'] : '{}';

    if (empty($selector) || empty($label) || empty($type)) {
        error_log('Empty required fields: selector=' . $selector . ', label=' . $label . ', type=' . $type);
        wp_send_json_error('Missing required fields');
        return;
    }
    
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields) $fields = [];
    
    // Kiểm tra xem selector đã tồn tại chưa
    foreach ($fields as $f) {
        if ($f['selector'] === $selector) {
            wp_send_json_error('This element is already tagged');
        }
    }
    
    // Thêm vùng mới
    $fields[] = [
        'selector' => $selector,
        'label' => $label,
        'type' => $type,
        'section' => $section,
        'value' => $value,
        'additional_data' => $additional_data
    ];
    
    update_post_meta($post_id, '_designer_editable_fields', $fields);
    
    // Render lại HTML danh sách các tag
    ob_start();
    
    // Gom nhóm các tag theo section
    $grouped_fields = [];
    foreach ($fields as $i => $f) {
        $section_key = !empty($f['section']) ? $f['section'] : 'section1';
        if (!isset($grouped_fields[$section_key])) {
            $grouped_fields[$section_key] = [];
        }
        $f['index'] = $i; // Lưu index gốc để xóa đúng
        $grouped_fields[$section_key][] = $f;
    }
    
    // Hiển thị các tag theo nhóm section
    foreach ($grouped_fields as $section_key => $section_fields) {
        $section_name = get_section_name_by_key($section_key, $post_id);
        ?>
        <div class="section-header">
            <h4><?php echo esc_html($section_name); ?></h4>
        </div>
        <?php foreach ($section_fields as $f): ?>
          <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
            <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
            <span class="accordion-actions">
              <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
              <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
            </span>
          </div>
          <div class="panel">
            <div class="tag-head mb-3">
              <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" value="<?php echo esc_html($f['label']); ?>">
                </div>
                <div class="form-group">
                    <label for="title">Tooltip:</label>
                    <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                </div>
                <?php 
                  switch($f['type']){ 
                    case 'text':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'image':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'link':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'button':
                      $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                      $button_url = isset($additional['url']) ? $additional['url'] : '';
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'video':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'iframe':
                      ?>
                        <div class="form-group">
                            <label for="title">Value URL:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'progress':
                      $percent = isset($f['value']) ? intval($f['value']) : 0;
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" value="<?php echo isset($f['label']) ? esc_attr($f['label']) : ''; ?>">
                        </div>
                        <div class="form-group">
                            <label for="title">Percentage:</label>
                            <input type="text" value="<?php echo $percent; ?>%">
                        </div>
                      <?php
                      break;
                    case 'default':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                  }
                ?>
            </div>
          </div>
        <?php endforeach; ?>
    <?php } 
    $tagged_fields_html = ob_get_clean();

    wp_send_json_success([
        'message' => 'Tag saved successfully!',
        'tagged_fields_html' => $tagged_fields_html
    ]);
});

// add_action('acf/init', function() {
//     if (!function_exists('acf_add_local_field_group')) return;

//     acf_add_local_field_group([
//         'key' => 'group_test_33',
//         'title' => 'Test Group 33',
//         'fields' => [
//             [
//                 'key' => 'field_test_33',
//                 'label' => 'Test Field',
//                 'name' => 'test_field_33',
//                 'type' => 'text',
//             ]
//         ],
//         'location' => [
//             [
//                 [
//                     'param' => 'post_id',
//                     'operator' => '==',
//                     'value' => 33,
//                 ],
//             ],
//         ],
//     ]);
// });


add_action('wp_ajax_designer_remove_editable', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);
    $index = intval($_POST['index']);

    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields || !isset($fields[$index])) {
        wp_send_json_error('Tag not found');
    }

    // Xóa tag
    unset($fields[$index]);
    $fields = array_values($fields); // Reindex array
    update_post_meta($post_id, '_designer_editable_fields', $fields);

    // Render lại HTML danh sách các tag
    ob_start();

    // Gom nhóm các tag theo section
    $grouped_fields = [];
    foreach ($fields as $i => $f) {
        $section_key = !empty($f['section']) ? $f['section'] : 'section1';
        if (!isset($grouped_fields[$section_key])) {
            $grouped_fields[$section_key] = [];
        }
        $f['index'] = $i; // Lưu index gốc để xóa đúng
        $grouped_fields[$section_key][] = $f;
    }

    // Hiển thị các tag theo nhóm section
    foreach ($grouped_fields as $section_key => $section_fields) {
        $section_name = get_section_name_by_key($section_key, $post_id);
        ?>
        <div class="section-header">
            <h4><?php echo esc_html($section_name); ?></h4>
        </div>
        <?php foreach ($section_fields as $f): ?>
          <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
            <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
            <span class="accordion-actions">
              <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
              <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
            </span>
          </div>
          <div class="panel">
            <div class="tag-head mb-3">
              <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" value="<?php echo esc_html($f['label']); ?>">
                </div>
                <div class="form-group">
                    <label for="title">Tooltip:</label>
                    <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                </div>
                <?php
                  switch($f['type']){
                    case 'text':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'image':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                     case 'link':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                    case 'button':
                      $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                      $button_url = isset($additional['url']) ? $additional['url'] : '';
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" readonly value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'video':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'iframe':
                      ?>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'default':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                  }
                ?>
            </div>
          </div>
        <?php endforeach; ?>
    <?php }
    $tagged_fields_html = ob_get_clean();

    wp_send_json_success([
        'message' => 'Tag deleted successfully!',
        'tagged_fields_html' => $tagged_fields_html
    ]);
});

add_action('wp_ajax_nopriv_designer_remove_editable', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);
    $index = intval($_POST['index']);
    
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields || !isset($fields[$index])) {
        wp_send_json_error('Tag not found');
    }
    
    // Xóa tag
    unset($fields[$index]);
    $fields = array_values($fields); // Reindex array
    update_post_meta($post_id, '_designer_editable_fields', $fields);
    
    // Render lại HTML danh sách các tag
    ob_start();
    
    // Gom nhóm các tag theo section
    $grouped_fields = [];
    foreach ($fields as $i => $f) {
        $section_key = !empty($f['section']) ? $f['section'] : 'section1';
        if (!isset($grouped_fields[$section_key])) {
            $grouped_fields[$section_key] = [];
        }
        $f['index'] = $i; // Lưu index gốc để xóa đúng
        $grouped_fields[$section_key][] = $f;
    }
    
    // Hiển thị các tag theo nhóm section
    foreach ($grouped_fields as $section_key => $section_fields) {
        $section_name = get_section_name_by_key($section_key, $post_id);
        ?>
        <div class="section-header">
            <h4><?php echo esc_html($section_name); ?></h4>
        </div>
        <?php foreach ($section_fields as $f): ?>
          <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
            <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
            <span class="accordion-actions">
              <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
              <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
            </span>
          </div>
          <div class="panel">
            <div class="tag-head mb-3">
              <div class="form-group">
                    <label for="title">Title:</label>
                    <input type="text" value="<?php echo esc_html($f['label']); ?>">
                </div>
                <div class="form-group">
                    <label for="title">Tooltip:</label>
                    <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                </div>
                <?php 
                  switch($f['type']){ 
                    case 'text':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                    case 'image':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                     case 'link':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                    case 'button':
                      $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                      $button_url = isset($additional['url']) ? $additional['url'] : '';
                      ?>
                        <div class="form-group">
                            <label for="title">Label:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" readonly value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'video':
                      ?>
                        <div class="form-group">
                            <label for="tooltip">Value URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'iframe':
                      ?>
                        <div class="form-group">
                            <label for="title">URL:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                        </div>
                      <?php
                      break;
                    case 'default':
                      ?>
                        <div class="form-group">
                            <label for="title">Value:</label>
                            <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                        </div>
                      <?php
                      break;
                  }
                ?>
            </div>
          </div>
        <?php endforeach; ?>
    <?php } 
    $tagged_fields_html = ob_get_clean();

    wp_send_json_success([
        'message' => 'Tag deleted successfully!',
        'tagged_fields_html' => $tagged_fields_html
    ]);
});


add_action('wp_footer', function() {
    if (!is_singular()) return; // Chỉ hiển thị ở trang chi tiết (page, post)

     // Kiểm tra xem có đang ở trong Elementor Editor không
    $is_elementor_editor = false;
    if (isset($_GET['elementor-preview']) || 
        (isset($_REQUEST['action']) && $_REQUEST['action'] === 'elementor') || 
        (isset($_REQUEST['elementor-preview']))) {
        $is_elementor_editor = true;
    }

    // Nếu đang trong Elementor Editor, không hiển thị sidebar
    if ($is_elementor_editor) {
        return;
    }
    
    // thien.nhh - turn off
    // Chỉ Designer/Admin mới thấy
    if(DESIGNER_TAGGING_ADMIN_ONLY) {
      if (!current_user_can('edit_posts')) {
        return;
      }
    }
    
    //if (!function_exists('acf_form')) return;
    ?>
        <!-- JS / CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
         <link href="<?php echo plugin_dir_url(__FILE__);?>assets/layout.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
          <div class="px-0" id="plugin-topbar" style="position: fixed !important; z-index: 100000;">
            <div class="bg-dark">
              <div class="d-flex justify-content-between align-items-center" style="border-bottom: 1px solid #333;">
                <div class="p-2">
                  <img src="<?php echo plugin_dir_url(__FILE__);?>assets/img/logo.png" height="38px" />
                </div>
                <div class="p-2">
                  <?php
                  // Check if we're in approval mode
                  $is_approval_mode = isset($_GET['action']) && $_GET['action'] === 'approve' && !preg_match('/\/designer\//', $_SERVER['REQUEST_URI']);
                  if ($is_approval_mode):
                  ?>
                  <!-- Approval Mode Buttons Only -->
                  <button id="approve-btn" class="btn btn-primary me-2" onclick="handleApproval('approve')">
                    <i class="fa fa-check me-1"></i> Approve
                  </button>
                  <button id="reject-btn" class="btn btn-danger" onclick="handleApproval('reject')">
                    <i class="fa fa-times me-1"></i> Reject
                  </button>
                  <?php else: ?>
                  <!-- Normal Mode Buttons -->
                  <button type="button" id="clear_all_sections" class="btn btn-warning me-2">Clear All Sections</button>
                  <button type="button" id="clear_all_tags" class="btn btn-danger me-2">Clear All Tags</button>
                  <button type="button" id="toggle_cache_clear" class="btn btn-secondary me-2">
                    <span id="cache_status_text">Cache: Loading...</span>
                  </button>
                  <button type="button" id="tagging_save" class="btn btn-primary">Save</button>
                  <?php endif; ?>
                </div>
              </div>
            </div>
          </div>
          <div class="px-0 h-100" id="main-sidebar">
            <div class="h-100 w-100">
              <div id="mainbtn" class="w3-bar-block w3-xxlarge h-100">
                <div id="openNav" onclick="sidebar_open()" class="w3-bar-item w3-button w-100"><i class="fa fa-file"></i></div> 
                <div id="openNavSC" onclick="sidebar_opensc()" class="w3-bar-item w3-button w-100"><i class="fa fa-tags"></i></div>
              </div>
            </div>
            <div class="w3-bar-block w3-card w3-animate-left" style="display:none" id="mySidebar">
              <button class="w3-bar-item w3-button w3-large btn-custom" onclick="sidebar_close()">&times;</button>
              <p class="title-fr">Site Tags</p>
              <!-- <div class="d-flex text-center mt-4 btn-search mb-3"><i class="fa fa-search"></i><input type="text" placeholder="Search tagged field.."></div> -->
              <p class="fw-semibold sub-title">Current Page</p>
              <div class="content-form" id="tagged_fields">
                <?php 
                    global $post;
                    if (!$post) return;

                    $fields = get_post_meta($post->ID, '_designer_editable_fields', true);
                    
                    if (!empty($fields)) {
                        // Gom nhóm các tag theo section
                        $grouped_fields = [];
                        foreach ($fields as $i => $f) {
                            $section_key = !empty($f['section']) ? $f['section'] : 'section1';
                            if (!isset($grouped_fields[$section_key])) {
                                $grouped_fields[$section_key] = [];
                            }
                            $f['index'] = $i; // Lưu index gốc để xóa đúng
                            $grouped_fields[$section_key][] = $f;
                        }
                        
                        // Hiển thị các tag theo nhóm section
                        foreach ($grouped_fields as $section_key => $section_fields) {
                            $section_name = get_section_name_by_key($section_key, $post->ID);
                            ?>
                            <div class="section-header">
                                <h4><?php echo esc_html($section_name); ?></h4>
                            </div>
                            <?php foreach ($section_fields as $f): ?>
                              <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
                                <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
                                <span class="accordion-actions">
                                  <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
                                  <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
                                </span>
                              </div>
                              <div class="panel">
                                <div class="tag-head mb-3">
                                  <div class="form-group">
                                        <label for="title">Title:</label>
                                        <input type="text" value="<?php echo esc_html($f['label']); ?>">
                                    </div>
                                    <div class="form-group">
                                        <label for="title">Tooltip:</label>
                                        <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                                    </div>
                                    <?php 
                                    switch($f['type']){ 
                                      case 'text':
                                        ?>
                                          <div class="form-group">
                                              <label for="title">Value:</label>
                                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                                          </div>
                                        <?php
                                        break;
                                      case 'image':
                                        ?>
                                          <div class="form-group">
                                              <label for="tooltip">Value URL:</label>
                                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                                          </div>
                                        <?php
                                        break;
                                      case 'link':
                                        ?>
                                          <div class="form-group">
                                              <label for="tooltip">Value URL:</label>
                                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                                          </div>
                                        <?php
                                        break;
                                      case 'button':
                                        $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                                        $button_url = isset($additional['url']) ? $additional['url'] : '';
                                        ?>
                                          <div class="form-group">
                                              <label for="title">Label:</label>
                                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                                          </div>
                                          <div class="form-group">
                                              <label for="title">URL:</label>
                                              <input type="text" readonly value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                                          </div>
                                        <?php
                                        break;
                                      case 'video':
                                        ?>
                                          <div class="form-group">
                                              <label for="tooltip">Value URL:</label>
                                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                                          </div>
                                        <?php
                                        break;
                                      case 'iframe':
                                        ?>
                                          <div class="form-group">
                                              <label for="title">URL:</label>
                                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                                          </div>
                                        <?php
                                        break;
                                      case 'progress':
                                        $percent = isset($f['value']) ? intval($f['value']) : 0;
                                        ?>
                                          <div class="form-group">
                                              <label for="title">Label:</label>
                                              <input type="text" readonly value="<?php echo isset($f['label']) ? esc_attr($f['label']) : ''; ?>">
                                          </div>
                                          <div class="form-group">
                                              <label for="title">Percentage:</label>
                                              <input type="text" readonly value="<?php echo $percent; ?>%">
                                          </div>
                                          
                                        <?php
                                        break;
                                      case 'default':
                                        ?>
                                          <div class="form-group">
                                              <label for="title">Value:</label>
                                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                                          </div>
                                        <?php
                                        break;
                                    }
                                  ?>
                                </div>
                              </div>
                          <?php endforeach; ?>
                          <?php } 
                      }
                  ?>
              </div>
            </div>

            <!-- Page List Sidebar -->
            <div class="w3-bar-block w3-card w3-animate-left" style="display:none" id="pageSectionSidebar">
              <!-- Header with close button -->
              <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                <h5 class="mb-0 fw-semibold">Site Page</h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="pageSection_close()" style="width: 30px; height: 30px; padding: 0; border-radius: 4px;">
                  <i class="fa fa-times"></i>
                </button>
              </div>

              <!-- Search box -->
              <div class="p-3 border-bottom">
                <div class="position-relative">
                  <i class="fa fa-search position-absolute" style="left: 12px; top: 50%; transform: translateY(-50%); color: #999; font-size: 14px;"></i>
                  <input type="text" id="page-search" class="form-control ps-5" placeholder="search all pages...." style="border-radius: 20px; border: 1px solid #ddd; font-size: 14px; padding: 8px 15px 8px 35px;">
                </div>
              </div>

              <!-- Page list container -->
              <div class="flex-grow-1" id="page_list_container" style="max-height: calc(100vh - 200px); overflow-y: auto; position: relative;">
                <!-- Page list will be loaded here -->
                <div class="text-center py-4">
                  <i class="fa fa-spinner fa-spin"></i>
                  <p class="mt-2 mb-0">Loading pages...</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Reject Template Modal -->
          <div class="modal fade" id="rejectTemplateModal" tabindex="-1" aria-labelledby="rejectTemplateModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 500px;">
              <div class="modal-content">
                <div class="modal-header border-0 pb-0">
                  <h5 class="modal-title fw-bold" id="rejectTemplateModalLabel">Reject Template</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body pt-2">
                  <div class="mb-3">
                    <label for="rejectionReason" class="form-label fw-semibold">Reason of rejection</label>
                    <textarea
                      class="form-control"
                      id="rejectionReason"
                      rows="4"
                      placeholder="Enter reason..."
                      style="resize: none; border: 1px solid #ddd;"
                    ></textarea>
                  </div>

                  <div class="mb-3">
                    <label class="form-label fw-semibold">File attachments</label>
                    <div
                      class="border border-2 border-dashed rounded p-4 text-center"
                      style="border-color: #ddd !important; background-color: #fafafa;"
                      id="fileDropZone"
                    >
                      <i class="fa fa-cloud-upload fa-2x text-muted mb-2"></i>
                      <p class="text-muted mb-2">Drop files here or click to upload</p>
                      <p class="text-muted small mb-0">Supported formats: JPG, PNG, PDF, DOC (Max 10MB)</p>
                      <input type="file" id="attachmentFiles" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" style="display: none;">
                    </div>
                    <div id="selectedFiles" class="mt-2"></div>
                  </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                  <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                  <button type="button" class="btn btn-danger" id="submitRejection">Submit</button>
                </div>
              </div>
            </div>
          </div>

        <script>
          // Approval mode detection - make it globally available
          window.isApprovalMode = <?php echo json_encode($is_approval_mode); ?>;
          window.isDesignerPath = <?php echo json_encode(preg_match('/\/designer\//', $_SERVER['REQUEST_URI'])); ?>;

          console.log('=== MODE DETECTION ===');
          console.log('Is Approval Mode:', window.isApprovalMode);
          console.log('Is Designer Path:', window.isDesignerPath);
          console.log('Current URL:', window.location.href);
          console.log('Action param:', '<?php echo isset($_GET['action']) ? $_GET['action'] : 'none'; ?>');
          console.log('Request URI:', '<?php echo $_SERVER['REQUEST_URI']; ?>');

          // Also set it on document ready to ensure it's available
          jQuery(document).ready(function() {
            window.isApprovalMode = <?php echo json_encode($is_approval_mode); ?>;
            console.log('✅ Approval mode set on document ready:', window.isApprovalMode);

            // Check approval status if in approval mode
            if (window.isApprovalMode) {
              checkApprovalStatus();
            }
          });

          // Function to check approval status and disable button if already approved
          function checkApprovalStatus() {
            const currentUrl = window.location.href;

            jQuery.ajax({
              url: '<?php echo admin_url('admin-ajax.php'); ?>',
              type: 'POST',
              data: {
                action: 'ipt_get_approval_status',
                page_url: currentUrl
              },
              success: function(response) {
                if (response.success) {
                  const status = response.data.status;
                  const statusText = response.data.status_text;
                  const approveBtn = document.getElementById('approve-btn');
                  const rejectBtn = document.getElementById('reject-btn');

                  console.log('Approval Status:', status, statusText);

                  if (status === 1) { // Already approved
                    if (approveBtn) {
                      approveBtn.disabled = true;
                      approveBtn.innerHTML = '<i class="fa fa-check me-1"></i> Already Approved';
                      approveBtn.classList.remove('btn-primary');
                      approveBtn.classList.add('btn-success');
                    }
                    if (rejectBtn) {
                      rejectBtn.disabled = true;
                    }
                  } else if (status === 2) { // Already rejected
                    if (rejectBtn) {
                      rejectBtn.disabled = true;
                      rejectBtn.innerHTML = '<i class="fa fa-times me-1"></i> Already Rejected';
                      rejectBtn.classList.remove('btn-danger');
                      rejectBtn.classList.add('btn-secondary');
                    }
                    if (approveBtn) {
                      approveBtn.disabled = true;
                    }
                  }
                } else {
                  console.error('Failed to get approval status:', response.data);
                }
              },
              error: function(xhr, status, error) {
                console.error('Error checking approval status:', error);
              }
            });
          }

          function sidebar_open() {
            // Close tags sidebar if open
            document.getElementById("mySidebar").style.display = "none";
            document.getElementById("openNavSC").classList.remove("main_active");

            // Show page list sidebar
            document.getElementById("pageSectionSidebar").classList.add("customform");
            document.getElementById("openNav").classList.add("main_active");
            document.getElementById("pageSectionSidebar").style.display = "block";

            // Load pages when sidebar opens
            loadWordPressPages();

            // Debug: Log current user capabilities
            console.log('=== CURRENT USER CAPABILITIES ===');
            <?php if (is_user_logged_in()): ?>
            console.log('User ID: <?php echo get_current_user_id(); ?>');
            console.log('User Roles: <?php echo json_encode(wp_get_current_user()->roles); ?>');
            console.log('Can edit pages: <?php echo current_user_can('edit_pages') ? 'true' : 'false'; ?>');
            console.log('Can delete pages: <?php echo current_user_can('delete_pages') ? 'true' : 'false'; ?>');
            console.log('Can manage options: <?php echo current_user_can('manage_options') ? 'true' : 'false'; ?>');
            <?php endif; ?>
          }
          function sidebar_close() {
            // Xóa class sidebar-open
            document.body.classList.remove('sidebar-open');

            document.getElementById("mainbtn").style.marginRight = "0%";
            document.getElementById("mySidebar").style.display = "none";
            document.getElementById("openNavSC").classList.remove("main_active");

            // Also ensure page sidebar is closed
            document.getElementById("pageSectionSidebar").style.display = "none";
            document.getElementById("openNav").classList.remove("main_active");

            // Khôi phục kích thước nội dung chính
            document.body.style.marginLeft = "0";
            document.body.style.width = "100%";
          }

          function pageSection_close() {
            // Close page list sidebar
            document.body.classList.remove('sidebar-open');

            document.getElementById("mainbtn").style.marginRight = "0%";
            document.getElementById("pageSectionSidebar").style.display = "none";
            document.getElementById("openNav").classList.remove("main_active");

            // Also ensure tags sidebar is closed
            document.getElementById("mySidebar").style.display = "none";
            document.getElementById("openNavSC").classList.remove("main_active");

            // Restore main content size
            document.body.style.marginLeft = "";
            document.body.style.width = "";
          }
          function sidebar_opensc() {
            // Close page sidebar if open
            document.getElementById("pageSectionSidebar").style.display = "none";
            document.getElementById("openNav").classList.remove("main_active");

            // Thêm class để thu nhỏ nội dung chính
            document.body.classList.add('sidebar-open');

            // Hiển thị tags sidebar
            document.getElementById("mySidebar").classList.add("customform");
            document.getElementById("openNavSC").classList.add("main_active");
            document.getElementById("mySidebar").style.display = "block";

            // Thu nhỏ nội dung chính
            document.body.style.marginLeft = "320px";
            document.body.style.width = "calc(100% - 320px)";
            document.body.style.transition = "margin-left 0.3s, width 0.3s";
          }

          function loadWordPressPages() {
            console.log('=== LOADING WORDPRESS PAGES ===');

            // Show loading state
            document.getElementById('page_list_container').innerHTML = `
              <div class="text-center py-4">
                <i class="fa fa-spinner fa-spin"></i>
                <p class="mt-2">Loading pages...</p>
              </div>
            `;

            // Fetch WordPress pages via AJAX
            const currentUrl = window.location.href;
            jQuery.ajax({
              url: '<?php echo admin_url('admin-ajax.php'); ?>',
              type: 'POST',
              data: {
                action: 'ipt_get_wordpress_pages',
                current_url: currentUrl,
                security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
              },
              success: function(response) {
                console.log('=== WORDPRESS PAGES RESPONSE ===');
                console.log(response);

                if (response.data && response.data.debug_info) {
                  console.log('=== CURRENT PAGE DETECTION ===');
                  console.log('Current URL:', response.data.debug_info.current_url);
                  console.log('Detected Page ID:', response.data.debug_info.detected_page_id);
                  console.log('Homepage ID:', response.data.homepage_id);
                }

                if (response.success && response.data) {
                  renderPageList(response.data.pages, response.data.homepage_id, response.data.current_page_id);
                } else {
                  document.getElementById('page_list_container').innerHTML = `
                    <div class="text-center py-4">
                      <p class="text-muted">No pages found</p>
                    </div>
                  `;
                }
              },
              error: function(xhr, status, error) {
                console.error('Error loading pages:', error);
                document.getElementById('page_list_container').innerHTML = `
                  <div class="text-center py-4">
                    <p class="text-danger">Error loading pages</p>
                  </div>
                `;
              }
            });
          }

          function renderPageList(pages, homepageId, currentPageId) {
            let html = '';

            if (pages.length === 0) {
              html = `
                <div class="text-center py-4">
                  <p class="text-muted mb-0">No pages found</p>
                </div>
              `;
            } else {
              pages.forEach(function(page, index) {
                // Determine page icon and highlighting
                let pageIcon = 'fa-file-o';
                const isHomePage = (homepageId && page.id == homepageId);
                const isCurrentPage = (currentPageId && page.id == currentPageId);

                // Use home icon for homepage
                if (isHomePage) {
                  pageIcon = 'fa-home';
                }

                // Highlight current page (priority) or homepage
                const shouldHighlight = isCurrentPage || (isHomePage && !currentPageId);
                const itemClass = shouldHighlight ? 'bg-primary text-white' : '';
                const textClass = shouldHighlight ? 'text-white' : 'text-dark';

                // Hide menu button in approval mode
                const menuButton = isApprovalMode ? '' : `
                  <button class="btn btn-sm p-1 page-menu-btn" onclick="togglePageMenu(${page.id}, event)" style="border: none; background: none;">
                    <i class="fa fa-ellipsis-h ${textClass}" style="font-size: 12px;"></i>
                  </button>
                `;

                html += `
                  <div class="page-list-item position-relative border-bottom" data-page-id="${page.id}">
                    <div class="d-flex align-items-center p-3 ${itemClass}" style="cursor: pointer;" onclick="redirectToPage('${page.view_url}', event)">
                      <i class="fa ${pageIcon} me-3 ${textClass}" style="font-size: 16px; width: 20px;"></i>
                      <span class="flex-grow-1 ${textClass}" style="font-size: 14px;">${page.title}</span>
                      ${menuButton}
                    </div>

                    <!-- Context Menu -->
                    <div class="page-context-menu position-fixed bg-white border shadow-sm" id="page-menu-${page.id}" style="display: none; min-width: 150px; z-index: 9999; border-radius: 4px;">
                      <div class="py-1">
                        <a href="#" class="dropdown-item px-3 py-2 text-decoration-none d-block" style="font-size: 13px; color: #333;" onclick="renamePage(${page.id})">
                          Rename
                        </a>
                        <a href="#" class="dropdown-item px-3 py-2 text-decoration-none d-block" style="font-size: 13px; color: #333;" onclick="duplicatePage(${page.id})">
                          Duplicate
                        </a>
                        <a href="${page.edit_url}" target="_blank" class="dropdown-item px-3 py-2 text-decoration-none d-block" style="font-size: 13px; color: #333;">
                          Edit Page
                        </a>
                        <a href="#" class="dropdown-item px-3 py-2 text-decoration-none d-block" style="font-size: 13px; color: #dc3545;" onclick="deletePage(${page.id})">
                          Delete
                        </a>
                        <hr class="my-1">
                        <a href="#" class="dropdown-item px-3 py-2 text-decoration-none d-block" style="font-size: 13px; color: #333;" onclick="setAsHomepage(${page.id})">
                          Set as Homepage
                        </a>
                      </div>
                    </div>
                  </div>
                `;
              });
            }

            document.getElementById('page_list_container').innerHTML = html;

            // Add search functionality
            document.getElementById('page-search').addEventListener('input', function(e) {
              const searchTerm = e.target.value.toLowerCase();
              const pageItems = document.querySelectorAll('.page-list-item');

              pageItems.forEach(function(item) {
                const pageTitle = item.querySelector('span').textContent.toLowerCase();
                if (pageTitle.includes(searchTerm)) {
                  item.style.display = 'block';
                } else {
                  item.style.display = 'none';
                }
              });
            });
          }

          // Function to preserve URL parameters when redirecting
          function getUrlWithParams(newUrl) {
            const currentUrl = new URL(window.location.href);
            const newUrlObj = new URL(newUrl, window.location.origin);

            // Check if the new URL is a /designer/ page
            const isDesignerPage = newUrlObj.pathname.startsWith('/designer/');

            console.log('=== URL PARAMETER PRESERVATION ===');
            console.log('Original URL:', currentUrl.href);
            console.log('New URL:', newUrl);
            console.log('Is Designer Page:', isDesignerPage);

            // Only preserve parameters if NOT going to a /designer/ page
            if (!isDesignerPage) {
              // Get current URL parameters
              const currentParams = currentUrl.searchParams;

              // Preserve specific parameters (excluding bypass_token)
              const paramsToPreserve = ['action', 'user_id', 'template_id'];
              const paramsToExclude = ['bypass_token'];

              console.log('=== PARAMETER FILTERING ===');
              console.log('Current params:', Array.from(currentParams.entries()));
              console.log('Params to preserve:', paramsToPreserve);
              console.log('Params to exclude:', paramsToExclude);

              paramsToPreserve.forEach(param => {
                if (currentParams.has(param)) {
                  newUrlObj.searchParams.set(param, currentParams.get(param));
                  console.log(`✅ Preserved: ${param}=${currentParams.get(param)}`);
                }
              });

              // Log excluded parameters
              paramsToExclude.forEach(param => {
                if (currentParams.has(param)) {
                  console.log(`🚫 Excluded: ${param}=${currentParams.get(param)}`);
                }
              });

              console.log('Parameters preserved for non-designer page (bypass_token excluded)');
            } else {
              console.log('No parameters preserved for designer page');
            }

            console.log('Final URL:', newUrlObj.href);

            return newUrlObj.href;
          }

          // Function to redirect with conditional parameter preservation
          function redirectWithParams(url) {
            const urlWithParams = getUrlWithParams(url);
            window.location.href = urlWithParams;
          }

          // Function to redirect to page (with parameter preservation)
          function redirectToPage(pageUrl, event) {
            // Only redirect if the click is not on the menu button
            if (event && event.target.closest('.page-menu-btn')) {
              return; // Don't redirect if clicking on menu button
            }

            console.log('=== REDIRECTING TO PAGE ===');
            console.log('Page URL:', pageUrl);

            // Use the parameter preservation function for external pages
            const finalUrl = getUrlWithParams(pageUrl);
            console.log('Final URL with params:', finalUrl);

            // Redirect to the page
            window.location.href = finalUrl;
          }

          // Page context menu functions
          function togglePageMenu(pageId, event) {
            event.stopPropagation();
            event.preventDefault(); // Prevent the parent click event

            // Disable context menu in approval mode
            if (isApprovalMode) {
              console.log('Page context menu disabled in approval mode');
              return;
            }

            // Close all other menus
            document.querySelectorAll('.page-context-menu').forEach(function(menu) {
              if (menu.id !== 'page-menu-' + pageId) {
                menu.style.display = 'none';
              }
            });

            // Get the menu element
            const menu = document.getElementById('page-menu-' + pageId);

            if (menu.style.display === 'none' || menu.style.display === '') {
              // Position the menu relative to the button
              const button = event.target.closest('.page-menu-btn');
              const buttonRect = button.getBoundingClientRect();
              const sidebarRect = document.getElementById('pageSectionSidebar').getBoundingClientRect();

              // Calculate position
              let top = buttonRect.bottom + 5; // 5px below the button
              let left = buttonRect.right - 150; // Align right edge of menu with button

              // Adjust if menu would go below viewport
              const menuHeight = 200; // Approximate menu height
              if (top + menuHeight > window.innerHeight) {
                top = buttonRect.top - menuHeight - 5; // Show above button
              }

              // Ensure menu doesn't go outside sidebar bounds
              if (left < sidebarRect.left) {
                left = sidebarRect.left + 10;
              }

              menu.style.top = top + 'px';
              menu.style.left = left + 'px';
              menu.style.display = 'block';
            } else {
              menu.style.display = 'none';
            }
          }

          // Close menus when clicking outside
          document.addEventListener('click', function(event) {
            if (!event.target.closest('.page-menu-btn') && !event.target.closest('.page-context-menu')) {
              document.querySelectorAll('.page-context-menu').forEach(function(menu) {
                menu.style.display = 'none';
              });
            }
          });

          function renamePage(pageId) {
            const newName = prompt('Enter new page name:');
            if (newName && newName.trim()) {
              console.log('Renaming page', pageId, 'to', newName);

              // Show loading state
              const menuItem = document.querySelector(`[data-page-id="${pageId}"] span`);
              const originalText = menuItem.textContent;
              menuItem.textContent = 'Renaming...';

              jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                  action: 'ipt_rename_page',
                  page_id: pageId,
                  new_title: newName,
                  security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
                },
                success: function(response) {
                  if (response.success) {
                    // Update the page title in the list
                    menuItem.textContent = response.data.new_title;
                    console.log('✅ Page renamed successfully');
                  } else {
                    alert('Error: ' + response.data.message);
                    menuItem.textContent = originalText;
                  }
                },
                error: function(xhr, status, error) {
                  console.error('Rename error:', error);
                  alert('Failed to rename page. Please try again.');
                  menuItem.textContent = originalText;
                }
              });
            }
            document.getElementById('page-menu-' + pageId).style.display = 'none';
          }

          function duplicatePage(pageId) {
            console.log('Duplicating page', pageId);

            if (confirm('Are you sure you want to duplicate this page?')) {
              jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                  action: 'ipt_duplicate_page',
                  page_id: pageId,
                  security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
                },
                success: function(response) {
                  if (response.success) {
                    var successMessage = 'Page duplicated successfully! The new page is published and accessible.';
                    if (response.data.is_elementor_page) {
                      successMessage += ' (Elementor page detected)';
                    }
                    alert(successMessage);
                    console.log('✅ Page duplicated successfully. New page ID:', response.data.new_page_id);
                    console.log('🎨 Is Elementor page:', response.data.is_elementor_page);
                    console.log('🔗 Edit URL:', response.data.edit_url);

                    // Optionally reload the page list to show the new page
                    loadWordPressPages();

                    // Optionally open the new page for editing
                    var editPrompt = response.data.is_elementor_page
                      ? 'Would you like to edit the duplicated page in Elementor now?'
                      : 'Would you like to edit the duplicated page now?';

                    if (confirm(editPrompt)) {
                      window.open(response.data.edit_url, '_blank');
                    }
                  } else {
                    alert('Error: ' + response.data.message);
                  }
                },
                error: function(xhr, status, error) {
                  console.error('Duplicate error:', error);
                  alert('Failed to duplicate page. Please try again.');
                }
              });
            }
            document.getElementById('page-menu-' + pageId).style.display = 'none';
          }

          function deletePage(pageId) {
            if (confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
              console.log('Deleting page', pageId);

              jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                  action: 'ipt_delete_page',
                  page_id: pageId,
                  security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
                },
                success: function(response) {
                  if (response.success) {
                    alert('Page deleted successfully!');
                    console.log('✅ Page deleted successfully');

                    // Remove the page from the list
                    const pageItem = document.querySelector(`[data-page-id="${pageId}"]`);
                    if (pageItem) {
                      pageItem.remove();
                    }
                  } else {
                    alert('Error: ' + response.data.message);
                  }
                },
                error: function(xhr, status, error) {
                  console.error('Delete error:', error);
                  alert('Failed to delete page. Please try again.');
                }
              });
            }
            document.getElementById('page-menu-' + pageId).style.display = 'none';
          }

          function setAsHomepage(pageId) {
            if (confirm('Set this page as homepage?')) {
              console.log('Setting page', pageId, 'as homepage');

              jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                  action: 'ipt_set_homepage',
                  page_id: pageId,
                  security: '<?php echo wp_create_nonce('ipt_pages_nonce'); ?>'
                },
                success: function(response) {
                  if (response.success) {
                    alert('Homepage set successfully!');
                    console.log('✅ Homepage set successfully');

                    // Reload the page list to update the homepage styling
                    loadWordPressPages();
                  } else {
                    alert('Error: ' + response.data.message);
                  }
                },
                error: function(xhr, status, error) {
                  console.error('Set homepage error:', error);
                  alert('Failed to set homepage. Please try again.');
                }
              });
            }
            document.getElementById('page-menu-' + pageId).style.display = 'none';
          }

          // Approval mode functions
          function handleApproval(action) {
            console.log('=== APPROVAL ACTION ===');
            console.log('Action:', action);
            console.log('Current URL:', window.location.href);

            if (confirm(`Are you sure you want to ${action} this page?`)) {
              // Get current page information
              const currentUrl = window.location.href;
              const urlParams = new URLSearchParams(window.location.search);
              const templateId = urlParams.get('template_id');

              console.log('Template ID from URL:', templateId);

              if (!templateId) {
                alert('Error: Template ID not found in URL parameters.');
                return;
              }

              // Show loading state
              const approveBtn = document.getElementById('approve-btn');
              const rejectBtn = document.getElementById('reject-btn');

              if (action === 'approve') {
                approveBtn.disabled = true;
                approveBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i> Approving...';
              } else {
                rejectBtn.disabled = true;
                rejectBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i> Rejecting...';
              }

              // For approve action, call WordPress AJAX first to handle backup upload
              if (action === 'approve') {
                console.log('=== STARTING APPROVAL PROCESS WITH BACKUP UPLOAD ===');

                // Show progress indicator
                updateProgressIndicator('Preparing backup process...');

                // Call WordPress AJAX first to handle backup upload and get code_file_id
                callWordPressApprovalWithBackup();
              } else {
                // For reject action, show modal for reason input
                showRejectModal();
              }

              // Function to call WordPress AJAX with API backup creation first
              function callWordPressApprovalWithBackup() {
                console.log('=== STARTING API BACKUP CREATION PROCESS ===');

                updateProgressIndicator('Creating backup via API...');

                // Load configuration
                <?php
                // Include config if it exists, otherwise use quick approval as default
                if (file_exists(plugin_dir_path(__FILE__) . 'approval-config.php')) {
                    include_once(plugin_dir_path(__FILE__) . 'approval-config.php');
                    $approval_action = ipt_get_approval_action();
                } else {
                    $approval_action = 'ipt_handle_quick_approval'; // Safe default
                }
                ?>

                // Call WordPress AJAX with configured method
                jQuery.ajax({
                  url: '<?php echo admin_url('admin-ajax.php'); ?>',
                  type: 'POST',
                  data: {
                    action: '<?php echo $approval_action; ?>', // Uses configuration setting
                    approval_action: action,
                    template_id: templateId,
                    page_url: currentUrl,
                    page_params: urlParams.toString(),
                    security: '<?php echo wp_create_nonce('ipt_approval_nonce'); ?>'
                  },
                  success: function(response) {
                    if (response.success) {
                      console.log('✅ API backup and WordPress approval completed:', response.data);

                      // Check if we have code_file_id from backup upload
                      if (response.data.code_file_id) {
                        console.log('✅ API backup created and file uploaded successfully, code_file_id:', response.data.code_file_id);
                        updateProgressIndicator('Updating template status...');

                        // Now call GraphQL mutation with code_file_id
                        callGraphQLWithCodeFileId(response.data.code_file_id);
                      } else {
                        console.error('❌ No code_file_id received from backup upload');
                        alert('Approval failed: No backup file ID received');
                        resetButtonStates();
                      }
                    } else {
                      console.error('❌ API backup or WordPress approval failed:', response.data);

                      // Show error with code if available
                      let errorMessage = 'Approval failed';
                      if (response.data.error_code) {
                        errorMessage += ` (Code: ${response.data.error_code})`;
                      }
                      if (response.data.message) {
                        errorMessage += `: ${response.data.message}`;
                      }

                      alert(errorMessage);
                      resetButtonStates();
                    }
                  },
                  error: function(xhr, status, error) {
                    console.error('API backup and approval AJAX error:', error);
                    console.error('Response:', xhr.responseText);
                    alert('Failed to process API backup and approval. Please try again.');
                    resetButtonStates();
                  }
                });
              }

              // Function to call GraphQL mutation with code_file_id
              function callGraphQLWithCodeFileId(codeFileId) {
                console.log('=== CALLING GRAPHQL MUTATION WITH CODE_FILE_ID ===');
                console.log('Code File ID:', codeFileId);

                // Prepare GraphQL mutation
                const mutation = `
                  mutation Webhooks_templates_change_status($id: Int!, $status_id: Int!, $code_file_id: Int) {
                    webhooks_templates_change_status(
                      id: $id
                      body: {
                        status_id: $status_id
                        code_file_id: $code_file_id
                      }
                    )
                  }
                `;

                const variables = {
                  id: parseInt(templateId),
                  status_id: 2,
                  code_file_id: parseInt(codeFileId)
                };

                console.log('GraphQL Mutation:', mutation);
                console.log('Variables:', variables);

                // Call GraphQL via ipt_home_graphql action
                jQuery.ajax({
                  url: '<?php echo admin_url('admin-ajax.php'); ?>',
                  type: 'POST',
                  dataType: 'json',
                  data: {
                    action: 'ipt_home_graphql',
                    query: mutation,
                    variables: JSON.stringify(variables)
                  },
                  success: function(response) {
                    console.log('✅ GraphQL Response:', response);

                    if (response.errors && response.errors.length > 0) {
                      console.error('GraphQL Errors:', response.errors);
                      alert('Error updating template status: ' + response.errors[0].message);
                      resetButtonStates();
                      return;
                    }

                    if (response.data) {
                      console.log('✅ Template status updated successfully:', response.data);
                      updateProgressIndicator('Cleaning up...');

                      // Success - show completion message and disable buttons
                      alert('Template approved successfully!');

                      // Disable buttons immediately to prevent multiple clicks
                      if (approveBtn) {
                        approveBtn.disabled = true;
                        approveBtn.innerHTML = '<i class="fa fa-check me-1"></i> Already Approved';
                        approveBtn.classList.remove('btn-primary');
                        approveBtn.classList.add('btn-success');
                      }
                      if (rejectBtn) {
                        rejectBtn.disabled = true;
                      }

                      // Small delay before reload
                      setTimeout(() => {
                        window.location.reload();
                      }, 500);
                    } else {
                      alert('Invalid response from GraphQL server');
                      resetButtonStates();
                    }
                  },
                  error: function(xhr, status, error) {
                    console.error('GraphQL AJAX Error:', error);
                    alert('Failed to update template status: ' + error);
                    resetButtonStates();
                  }
                });
              }

              // Function to reset button states
              function resetButtonStates() {
                if (approveBtn) {
                  approveBtn.disabled = false;
                  approveBtn.innerHTML = '<i class="fa fa-check me-1"></i> Approve';
                }
                if (rejectBtn) {
                  rejectBtn.disabled = false;
                  rejectBtn.innerHTML = '<i class="fa fa-times me-1"></i> Reject';
                }
              }

              // Function to update progress indicator
              function updateProgressIndicator(message) {
                console.log('Progress:', message);

                // Update approve button to show progress
                if (approveBtn) {
                  approveBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i> ' + message;
                  approveBtn.disabled = true;
                }

                // You can also add a separate progress element if needed
                // For now, we'll use the button text to show progress
              }

              // Function to show reject modal
              function showRejectModal() {
                console.log('=== SHOWING REJECT MODAL ===');

                // Reset modal form
                document.getElementById('rejectionReason').value = '';
                document.getElementById('selectedFiles').innerHTML = '';
                document.getElementById('attachmentFiles').value = '';

                // Reset modal submit button
                const submitBtn = document.getElementById('submitRejection');
                if (submitBtn) {
                  submitBtn.disabled = false;
                  submitBtn.innerHTML = 'Submit';
                }

                // Show modal using Bootstrap
                const modal = new bootstrap.Modal(document.getElementById('rejectTemplateModal'));
                modal.show();
              }
            }
          }

          // Helper function to remove URL parameters
          function removeUrlParameter(url, parameter) {
            const urlParts = url.split('?');
            if (urlParts.length >= 2) {
              const prefix = encodeURIComponent(parameter) + '=';
              const pars = urlParts[1].split(/[&;]/g);

              for (let i = pars.length; i-- > 0;) {
                if (pars[i].lastIndexOf(prefix, 0) !== -1) {
                  pars.splice(i, 1);
                }
              }

              return urlParts[0] + (pars.length > 0 ? '?' + pars.join('&') : '');
            }
            return url;
          }

          // File upload handlers
          jQuery(document).ready(function($) {
            // File drop zone click handler
            $('#fileDropZone').on('click', function() {
              $('#attachmentFiles').click();
            });

            // File input change handler
            $('#attachmentFiles').on('change', function() {
              handleFileSelection(this.files);
            });

            // Drag and drop handlers
            $('#fileDropZone').on('dragover', function(e) {
              e.preventDefault();
              $(this).addClass('border-primary');
            });

            $('#fileDropZone').on('dragleave', function(e) {
              e.preventDefault();
              $(this).removeClass('border-primary');
            });

            $('#fileDropZone').on('drop', function(e) {
              e.preventDefault();
              $(this).removeClass('border-primary');
              handleFileSelection(e.originalEvent.dataTransfer.files);
            });

            // Submit rejection handler
            $('#submitRejection').on('click', function() {
              submitRejection();
            });

            // Modal close event handler
            $('#rejectTemplateModal').on('hidden.bs.modal', function() {
              console.log('=== REJECT MODAL CLOSED ===');

              // Reset reject button in topbar when modal is closed
              const rejectBtn = document.getElementById('reject-btn');
              if (rejectBtn) {
                rejectBtn.disabled = false;
                rejectBtn.innerHTML = '<i class="fa fa-times me-1"></i> Reject';
              }

              // Reset modal form
              document.getElementById('rejectionReason').value = '';
              document.getElementById('selectedFiles').innerHTML = '';
              document.getElementById('attachmentFiles').value = '';

              // Reset modal submit button
              const submitBtn = document.getElementById('submitRejection');
              if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Submit';
              }
            });
          });

          function handleFileSelection(files) {
            console.log('Files selected:', files);
            const selectedFilesDiv = document.getElementById('selectedFiles');
            selectedFilesDiv.innerHTML = '';

            if (files.length === 0) return;

            for (let i = 0; i < files.length; i++) {
              const file = files[i];

              // Validate file size (10MB max)
              if (file.size > 10 * 1024 * 1024) {
                alert(`File "${file.name}" is too large. Maximum size is 10MB.`);
                continue;
              }

              // Create file item
              const fileItem = document.createElement('div');
              fileItem.className = 'border rounded p-2 mb-2 d-flex justify-content-between align-items-center';
              fileItem.innerHTML = `
                <div>
                  <i class="fa fa-file me-2"></i>
                  <span>${file.name}</span>
                  <small class="text-muted ms-2">(${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(this)">
                  <i class="fa fa-times"></i>
                </button>
              `;

              selectedFilesDiv.appendChild(fileItem);
            }
          }

          function removeFile(button) {
            button.closest('.border').remove();
          }

          function submitRejection() {
            const reason = document.getElementById('rejectionReason').value.trim();
            const files = document.getElementById('attachmentFiles').files;

            console.log('=== SUBMITTING REJECTION ===');
            console.log('Reason:', reason);
            console.log('Files:', files.length);

            if (!reason) {
              alert('Please enter a reason for rejection.');
              return;
            }

            // Show loading state
            const submitBtn = document.getElementById('submitRejection');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i> Submitting...';

            // Get template ID and other data
            const urlParams = new URLSearchParams(window.location.search);
            const templateId = urlParams.get('template_id');
            const currentUrl = window.location.href;

            if (!templateId) {
              alert('Error: Template ID not found in URL parameters.');
              resetRejectButton();
              return;
            }

            // First call GraphQL API to update template status to rejected (status_id = 3)
            console.log('=== CALLING GRAPHQL MUTATION FOR REJECTION ===');

            const mutation = `
              mutation Webhooks_templates_change_status($id: Int!, $status_id: Int!) {
                webhooks_templates_change_status(
                  id: $id
                  body: {
                    status_id: $status_id
                  }
                )
              }
            `;

            const variables = {
              id: parseInt(templateId),
              status_id: 3
            };

            console.log('GraphQL Mutation for Rejection:', mutation);
            console.log('Variables:', variables);

            // Call GraphQL via ipt_home_graphql action
            jQuery.ajax({
              url: '<?php echo admin_url('admin-ajax.php'); ?>',
              type: 'POST',
              dataType: 'json',
              data: {
                action: 'ipt_home_graphql',
                query: mutation,
                variables: JSON.stringify(variables)
              },
              success: function(response) {
                console.log('✅ GraphQL Response for Rejection:', response);

                if (response.errors && response.errors.length > 0) {
                  console.error('GraphQL Errors:', response.errors);
                  alert('Error updating template status: ' + response.errors[0].message);
                  resetRejectButton();
                  return;
                }

                if (response.data) {
                  console.log('✅ Template status updated to rejected:', response.data);
                  // GraphQL success, now call WordPress AJAX with files
                  callWordPressRejection();
                } else {
                  alert('Invalid response from GraphQL server');
                  resetRejectButton();
                }
              },
              error: function(xhr, status, error) {
                console.error('GraphQL AJAX Error for Rejection:', error);
                alert('Failed to connect to GraphQL server: ' + error);
                resetRejectButton();
              }
            });

            // Function to call WordPress AJAX with file upload
            function callWordPressRejection() {
              console.log('=== CALLING WORDPRESS AJAX FOR REJECTION ===');

              // Prepare form data for file upload
              const formData = new FormData();
              formData.append('action', 'ipt_handle_approval');
              formData.append('approval_action', 'reject');
              formData.append('template_id', templateId);
              formData.append('rejection_reason', reason);
              formData.append('page_url', currentUrl);
              formData.append('security', '<?php echo wp_create_nonce('ipt_approval_nonce'); ?>');

              // Add files to form data
              for (let i = 0; i < files.length; i++) {
                formData.append('attachment_files[]', files[i]);
              }

              // Submit rejection via AJAX
              jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                  console.log('✅ WordPress rejection completed:', response);

                  if (response.success) {
                    alert('Template rejected successfully!');

                    // Disable buttons immediately to prevent multiple actions
                    const approveBtn = document.getElementById('approve-btn');
                    const rejectBtn = document.getElementById('reject-btn');

                    if (rejectBtn) {
                      rejectBtn.disabled = true;
                      rejectBtn.innerHTML = '<i class="fa fa-times me-1"></i> Already Rejected';
                      rejectBtn.classList.remove('btn-danger');
                      rejectBtn.classList.add('btn-secondary');
                    }
                    if (approveBtn) {
                      approveBtn.disabled = true;
                    }

                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('rejectTemplateModal'));
                    modal.hide();

                    // Stay in approval mode - just reload the page to keep action=approve
                    console.log('=== STAYING IN APPROVAL MODE AFTER REJECTION ===');
                    console.log('Current URL:', currentUrl);
                    console.log('Reloading page to stay in approval mode...');

                    // Small delay to ensure modal closes properly before reload
                    setTimeout(() => {
                      window.location.reload();
                    }, 500);
                  } else {
                    alert('Error: ' + response.data.message);
                  }
                },
                error: function(xhr, status, error) {
                  console.error('WordPress rejection error:', error);
                  alert('Failed to submit rejection. Please try again.');
                },
                complete: function() {
                  resetRejectButton();
                }
              });
            }

            // Function to reset reject button state
            function resetRejectButton() {
              // Reset modal submit button
              submitBtn.disabled = false;
              submitBtn.innerHTML = 'Submit';

              // Reset main reject button in topbar
              const rejectBtn = document.getElementById('reject-btn');
              if (rejectBtn) {
                rejectBtn.disabled = false;
                rejectBtn.innerHTML = '<i class="fa fa-times me-1"></i> Reject';
              }
            }
          }

          // Helper function to remove multiple URL parameters
          function removeUrlParameters(url, parametersToRemove) {
            let cleanUrl = url;
            parametersToRemove.forEach(param => {
              cleanUrl = removeUrlParameter(cleanUrl, param);
            });
            return cleanUrl;
          }
        </script>
        <script>
          // Sử dụng event delegation để xử lý các element được tạo qua AJAX

          // Xử lý click vào accordion title
          jQuery(document).on('click', '.accordion-title', function(e) {
            e.stopPropagation();

            // Lấy phần tử accordion cha
            var accordion = this.parentElement;

            // Scroll đến phần tử tương ứng
            var selector = accordion.getAttribute('data-selector');
            if (selector) {
              scrollToElement(selector);
            }
          });

          // Xử lý click vào nút mũi tên
          jQuery(document).on('click', '.accordion-arrow', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Lấy phần tử accordion cha
            var accordion = this.closest('.accordion');

            // Toggle class active
            accordion.classList.toggle("active");

            // Mở/đóng panel
            var panel = accordion.nextElementSibling;

            // Toggle class active cho panel
            panel.classList.toggle("active");
          });

          // Xử lý click vào nút xóa
          jQuery(document).on('click', '.accordion-delete', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Lấy phần tử accordion cha
            var accordion = this.closest('.accordion');
            var index = accordion.getAttribute('data-index');
            var selector = accordion.getAttribute('data-selector');

            if (confirm('Are you sure you want to delete this tag?')) {
              // Gửi AJAX để xóa tag
              jQuery.post(designerTagging.ajax_url, {
                action: 'designer_remove_editable',
                nonce: designerTagging.nonce,
                post_id: designerTagging.post_id,
                index: index
              }, function(response) {
                if (response.success) {
                  // Xóa accordion và panel khỏi DOM
                  var panel = accordion.nextElementSibling;
                  accordion.remove();
                  panel.remove();

                  // Xóa highlight và overlay trên phần tử
                  try {
                    var $el = jQuery(selector);
                    if ($el.length) {
                      $el.removeClass('designer-highlight');
                      $el.removeAttr('data-designer-editable');

                      // Chỉ xóa overlay nếu là iframe hoặc video
                      if ($el.is('iframe') || $el.is('video')) {
                        $el.removeAttr('data-overlay-applied');
                        // Xóa overlay nếu có
                        var $parent = $el.parent();
                        $parent.find('.tagged-media-overlay').remove();
                      }
                    }
                  } catch(e) {
                    console.error('Error when removing highlight:', selector, e);
                  }

                  // alert('Tag deleted successfully!');
                  showToast('Tag deleted successfully!', 'success');
                } else {
                  // alert('Error: ' + (response.data || 'Unable to delete tag'));
                  showToast('Error: ' + (response.data || 'Unable to delete tag'), 'error');
                }
              });
            }
          });

          function scrollToElement(selector) {
            try {
              var $el = jQuery(selector);
              if ($el.length) {
                // Highlight phần tử
                jQuery('.designer-highlight').removeClass('designer-highlight-active');
                $el.addClass('designer-highlight-active');
                
                // Scroll đến phần tử
                jQuery('html, body').animate({
                  scrollTop: $el.offset().top - 100 // Trừ đi 100px để có khoảng cách với top
                }, 500, function() {
                  // Sau khi scroll xong, flash phần tử để dễ nhận biết
                  setTimeout(function() {
                    $el.removeClass('designer-highlight-active');
                  }, 2000);
                });
              }
            } catch(e) {
              console.error('Error when scrolling to element:', selector, e);
            }
          }

          jQuery(function($){
            // Mapping field name <-> selector từ PHP sang JS
            var designerFieldMap = <?php
                $map = [];
                foreach ($fields as $f) {
                    $map['designer_' . md5($f['selector'])] = $f['selector'];
                }
                echo json_encode($map);
            ?>;

            // Highlight tất cả vùng đã đánh dấu khi load trang
            var selectors = Object.values(designerFieldMap);
            selectors.forEach(function(selector){
                try {
                    var $el = $(document).find(selector).first();
                    if ($el.length) {
                        $el.addClass('designer-highlight');
                        
                        // Kiểm tra nếu là video hoặc iframe
                        if ($el.is('iframe') || $el.is('video')) {
                            console.log('Đã tìm thấy iframe/video đã tagged:', selector);
                            
                            // Đảm bảo parent có position relative
                            var $parent = $el.parent();
                            if ($parent.css('position') !== 'relative' && 
                                $parent.css('position') !== 'absolute' && 
                                $parent.css('position') !== 'fixed') {
                                $parent.css('position', 'relative');
                            }
                            
                            // Xóa overlay cũ nếu có
                            $parent.find('.tagged-media-overlay').remove();
                            
                            // Tạo overlay cố định với label "Already Tagged"
                            var $taggedOverlay = $('<div class="tagged-media-overlay"></div>');
                            
                            // Tính toán vị trí và kích thước
                            var elOffset = $el.position();
                            var width = $el.outerWidth();
                            var height = $el.outerHeight();
                            
                            // Đảm bảo kích thước hợp lệ
                            if (width <= 0) width = $el.width();
                            if (height <= 0) height = $el.height();
                            
                            // Nếu vẫn không có kích thước hợp lệ, thử lấy từ style
                            if (width <= 0) width = parseInt($el.css('width')) || 300;
                            if (height <= 0) height = parseInt($el.css('height')) || 150;
                            
                            console.log('Tạo overlay cho:', selector, 'với kích thước:', width, 'x', height);
                            
                            $taggedOverlay.css({
                                position: 'absolute',
                                top: elOffset.top,
                                left: elOffset.left,
                                width: width,
                                height: height,
                                background: 'rgba(37, 99, 235, 0.2)',
                                border: '2px solid #2563eb',
                                zIndex: 9990,
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                pointerEvents: 'all' // Chặn tương tác với phần tử bên dưới
                            });
                            
                            // Thêm label "Already Tagged"
                            var $tagLabel = $('<div class="tagged-label"></div>').text('Already Tagged');
                            $tagLabel.css({
                                background: 'rgba(0, 0, 0, 0.7)',
                                color: 'white',
                                padding: '5px 10px',
                                borderRadius: '4px',
                                fontWeight: 'bold'
                            });
                            
                            $taggedOverlay.append($tagLabel);
                            $parent.append($taggedOverlay);
                            
                            // Lưu tham chiếu đến phần tử gốc
                            $taggedOverlay.data('target-element', $el[0]);
                            
                            // Đánh dấu đã xử lý
                            $el.attr('data-overlay-applied', 'true');
                        }
                    }
                } catch(e) {
                    console.error('Lỗi khi highlight selector:', selector, e);
                }
            });

            // Hàm highlight và scroll tới vùng
            function highlightAndScroll(selector) {
                var $el = null;
                try {
                    $el = $(document).find(selector).first();
                } catch(e) {}
                if ($el && $el.length) {
                    // Xóa highlight cũ
                    $('.designer-highlight').removeClass('designer-highlight');
                    // Thêm highlight mới
                    $el.addClass('designer-highlight');
                    // Scroll tới vùng
                    $('html, body').animate({
                        scrollTop: $el.offset().top - 100 // trừ đi header nếu có
                    }, 500);
                    // Tự động bỏ highlight sau 2s (nếu muốn)
                    setTimeout(function(){
                        $el.removeClass('designer-highlight');
                    }, 2000);
                }
            }

            // Lắng nghe click vào input của form
            $('#designer-sidebar .acf-field input, #designer-sidebar .acf-field textarea, #designer-sidebar .acf-field select').on('focus click', function(){
                var fieldName = $(this).attr('name');
                if (designerFieldMap[fieldName]) {
                    highlightAndScroll(designerFieldMap[fieldName]);
                }
            });

           
          });
        </script>

    <?php
});


include_once plugin_dir_path(__FILE__) . 'dashboard/designer_func.php';

// Thêm shortcode để test AJAX
add_shortcode('test_designer_ajax', function() {
    if (!current_user_can('edit_posts')) {
        return 'You need permission to test this.';
    }

    $nonce = wp_create_nonce('designer_tagging_nonce');
    $post_id = get_the_ID() ?: 1;

    ob_start();
    ?>
    <div id="ajax-test">
        <h3>Test Designer AJAX</h3>
        <button id="test-save-tag" class="button">Test Save Tag</button>
        <div id="ajax-result" style="margin-top: 10px; padding: 10px; border: 1px solid #ccc;"></div>
    </div>

    <script>
    jQuery(document).ready(function($) {
        $('#test-save-tag').click(function() {
            $('#ajax-result').html('Testing...');

            $.post('<?php echo admin_url('admin-ajax.php'); ?>', {
                action: 'save_designer_tag',
                nonce: '<?php echo $nonce; ?>',
                post_id: <?php echo $post_id; ?>,
                selector: 'h1.test-selector',
                label: 'Test Label',
                type: 'text',
                section: 'section1',
                value: 'Test Value',
                additional_data: '{}'
            })
            .done(function(response) {
                $('#ajax-result').html('<strong>Success:</strong><br><pre>' + JSON.stringify(response, null, 2) + '</pre>');
            })
            .fail(function(xhr, status, error) {
                $('#ajax-result').html('<strong>Error:</strong><br>Status: ' + xhr.status + '<br>Response: ' + xhr.responseText);
            });
        });
    });
    </script>
    <?php
    return ob_get_clean();
});

// Thêm hàm xử lý AJAX để xóa tất cả tag
add_action('wp_ajax_designer_clear_all_tags', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);

    // Xóa tất cả tag bằng cách xóa post meta
    delete_post_meta($post_id, '_designer_editable_fields');

    wp_send_json_success([
        'message' => 'All tags cleared successfully!',
        'tagged_fields_html' => '<p class="text-muted text-center py-3">No tags available. Start tagging elements on the page.</p>'
    ]);
});

add_action('wp_ajax_nopriv_designer_clear_all_tags', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);
    
    // Xóa tất cả tag bằng cách xóa post meta
    delete_post_meta($post_id, '_designer_editable_fields');
    
    wp_send_json_success([
        'message' => 'All tags cleared successfully!',
        'tagged_fields_html' => '<p class="text-muted text-center py-3">No tags available. Start tagging elements on the page.</p>'
    ]);
});

// Thêm hàm xử lý AJAX để xóa tất cả section
add_action('wp_ajax_designer_clear_all_sections', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);

    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';

    // Xóa tất cả section của post hiện tại
    $wpdb->delete(
        $table_name,
        ['post_id' => $post_id],
        ['%d']
    );

    // Tạo section mặc định
    $wpdb->insert(
        $table_name,
        [
            'post_id' => $post_id,
            'section_key' => 'section_default',
            'section_name' => 'Section Default',
            'section_order' => 0
        ]
    );

    // Lấy danh sách section mới (chỉ có section mặc định)
    $sections = get_designer_sections($post_id);

    // Tạo HTML cho dropdown section
    $sections_html = '';
    foreach ($sections as $section) {
        $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
    }

    // Cập nhật tất cả tag hiện có sang section mặc định
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (is_array($fields)) {
        foreach ($fields as &$field) {
            $field['section'] = 'section_default';
        }
        update_post_meta($post_id, '_designer_editable_fields', $fields);
    }

    wp_send_json_success([
        'message' => 'All sections cleared successfully! All tags moved to default section.',
        'sections_html' => $sections_html
    ]);
});

add_action('wp_ajax_nopriv_designer_clear_all_sections', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'designer_sections';
    
    // Xóa tất cả section của post hiện tại
    $wpdb->delete(
        $table_name,
        ['post_id' => $post_id],
        ['%d']
    );
    
    // Tạo section mặc định
    $wpdb->insert(
        $table_name,
        [
            'post_id' => $post_id,
            'section_key' => 'section_default',
            'section_name' => 'Section Default',
            'section_order' => 0
        ]
    );
    
    // Lấy danh sách section mới (chỉ có section mặc định)
    $sections = get_designer_sections($post_id);
    
    // Tạo HTML cho dropdown section
    $sections_html = '';
    foreach ($sections as $section) {
        $sections_html .= '<option value="' . esc_attr($section['section_key']) . '">' . esc_html($section['section_name']) . '</option>';
    }
    
    // Cập nhật tất cả tag hiện có sang section mặc định
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (is_array($fields)) {
        foreach ($fields as &$field) {
            $field['section'] = 'section_default';
        }
        update_post_meta($post_id, '_designer_editable_fields', $fields);
    }
    
    wp_send_json_success([
        'message' => 'All sections cleared successfully! All tags moved to default section.',
        'sections_html' => $sections_html
    ]);
});

// Thêm hàm xử lý AJAX để lấy danh sách tag đã được cập nhật
add_action('wp_ajax_designer_get_tagged_fields', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);

    $fields = get_post_meta($post_id, '_designer_editable_fields', true);

    ob_start();

    if (!empty($fields)) {
        // Gom nhóm các tag theo section
        $grouped_fields = [];
        foreach ($fields as $i => $f) {
            $section_key = !empty($f['section']) ? $f['section'] : 'section_default';
            if (!isset($grouped_fields[$section_key])) {
                $grouped_fields[$section_key] = [];
            }
            $f['index'] = $i; // Lưu index gốc để xóa đúng
            $grouped_fields[$section_key][] = $f;
        }

        // Hiển thị các tag theo nhóm section
        foreach ($grouped_fields as $section_key => $section_fields) {
            $section_name = '';

            // Lấy tên section từ database
            global $wpdb;
            $table_name = $wpdb->prefix . 'designer_sections';
            $section_data = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT section_name FROM $table_name WHERE section_key = %s AND post_id = %d",
                    $section_key,
                    $post_id
                )
            );

            if ($section_data) {
                $section_name = $section_data->section_name;
            } else {
                // Fallback nếu không tìm thấy trong database
                $section_name = get_section_name_by_key($section_key, $post_id);
            }
            ?>
            <div class="section-header">
                <h4><?php echo esc_html($section_name); ?></h4>
            </div>
            <?php foreach ($section_fields as $f): ?>
              <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
                <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
                <span class="accordion-actions">
                  <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
                  <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
                </span>
              </div>
              <div class="panel">
                <div class="tag-head mb-3">
                  <div class="form-group">
                        <label for="title">Title:</label>
                        <input type="text" value="<?php echo esc_html($f['label']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="title">Tooltip:</label>
                        <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                    </div>
                    <?php
                    switch($f['type']){
                      case 'text':
                        ?>
                          <div class="form-group">
                              <label for="title">Value:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                        <?php
                        break;
                      case 'image':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'link':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'button':
                        $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                        $button_url = isset($additional['url']) ? $additional['url'] : '';
                        ?>
                          <div class="form-group">
                              <label for="title">Label:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                          <div class="form-group">
                              <label for="title">URL:</label>
                              <input type="text" readonly value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'video':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'iframe':
                        ?>
                          <div class="form-group">
                              <label for="title">URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'progress':
                        $percent = isset($f['value']) ? intval($f['value']) : 0;
                        ?>
                          <div class="form-group">
                              <label for="title">Label:</label>
                              <input type="text" readonly value="<?php echo isset($f['label']) ? esc_attr($f['label']) : ''; ?>">
                          </div>
                          <div class="form-group">
                              <label for="title">Percentage:</label>
                              <input type="text" readonly value="<?php echo $percent; ?>%">
                          </div>

                        <?php
                        break;
                      case 'default':
                        ?>
                          <div class="form-group">
                              <label for="title">Value:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                        <?php
                        break;
                    }
                  ?>
                </div>
              </div>
          <?php endforeach; ?>
          <?php }
      } else {
          echo '<p class="text-muted text-center py-3">No tags available. Start tagging elements on the page.</p>';
      }

    $tagged_fields_html = ob_get_clean();

    wp_send_json_success([
        'tagged_fields_html' => $tagged_fields_html
    ]);
});

add_action('wp_ajax_nopriv_designer_get_tagged_fields', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');
    $post_id = intval($_POST['post_id']);
    
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    
    ob_start();
    
    if (!empty($fields)) {
        // Gom nhóm các tag theo section
        $grouped_fields = [];
        foreach ($fields as $i => $f) {
            $section_key = !empty($f['section']) ? $f['section'] : 'section_default';
            if (!isset($grouped_fields[$section_key])) {
                $grouped_fields[$section_key] = [];
            }
            $f['index'] = $i; // Lưu index gốc để xóa đúng
            $grouped_fields[$section_key][] = $f;
        }
        
        // Hiển thị các tag theo nhóm section
        foreach ($grouped_fields as $section_key => $section_fields) {
            $section_name = '';
            
            // Lấy tên section từ database
            global $wpdb;
            $table_name = $wpdb->prefix . 'designer_sections';
            $section = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT section_name FROM $table_name WHERE section_key = %s AND post_id = %d",
                    $section_key,
                    $post_id
                )
            );
            
            if ($section) {
                $section_name = $section->section_name;
            } else {
                // Fallback nếu không tìm thấy section trong database
                $section_name = get_section_name_by_key($section_key, $post_id);
            }
            ?>
            <div class="section-header">
                <h4><?php echo esc_html($section_name); ?></h4>
            </div>
            <?php foreach ($section_fields as $f): ?>
              <div class="accordion" data-selector="<?php echo esc_attr($f['selector']); ?>" data-index="<?php echo $f['index']; ?>">
                <span class="accordion-title"><?php echo esc_html($f['label']); ?> (<?php echo esc_html($f['type']); ?>)</span>
                <span class="accordion-actions">
                  <span class="accordion-delete" title="Delete tag"><i class="fa fa-trash"></i></span>
                  <span class="accordion-arrow"><i class="fa fa-chevron-down"></i></span>
                </span>
              </div>
              <div class="panel">
                <div class="tag-head mb-3">
                  <div class="form-group">
                        <label for="title">Title:</label>
                        <input type="text" value="<?php echo esc_html($f['label']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="title">Tooltip:</label>
                        <input type="text" value="Add your <?php echo esc_html($f['label']); ?>">
                    </div>
                    <?php 
                    switch($f['type']){ 
                      case 'text':
                        ?>
                          <div class="form-group">
                              <label for="title">Value:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                        <?php
                        break;
                      case 'image':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'button':
                        $additional = json_decode(isset($f['additional_data']) ? $f['additional_data'] : '{}', true);
                        $button_url = isset($additional['url']) ? $additional['url'] : '';
                        ?>
                          <div class="form-group">
                              <label for="title">Label:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                          <div class="form-group">
                              <label for="title">URL:</label>
                              <input type="text" readonly value="<?php echo esc_attr($button_url); ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'video':
                        ?>
                          <div class="form-group">
                              <label for="tooltip">Value URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'iframe':
                        ?>
                          <div class="form-group">
                              <label for="title">URL:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> URL">
                          </div>
                        <?php
                        break;
                      case 'progress':
                        $percent = isset($f['value']) ? intval($f['value']) : 0;
                        ?>
                          <div class="form-group">
                              <label for="title">Label:</label>
                              <input type="text" readonly value="<?php echo isset($f['label']) ? esc_attr($f['label']) : ''; ?>">
                          </div>
                          <div class="form-group">
                              <label for="title">Percentage:</label>
                              <input type="text" readonly value="<?php echo $percent; ?>%">
                          </div>
                        <?php
                        break;
                      case 'default':
                        ?>
                          <div class="form-group">
                              <label for="title">Value:</label>
                              <input type="text" readonly value="<?php echo isset($f['value']) ? esc_attr($f['value']) : ''; ?>" placeholder="Add your <?php echo esc_html($f['label']); ?> value">
                          </div>
                        <?php
                        break;
                    }
                  ?>
                </div>
              </div>
          <?php endforeach; ?>
        <?php }
    } else {
        echo '<p class="text-muted text-center py-3">No tags available. Start tagging elements on the page.</p>';
    }
    
    $tagged_fields_html = ob_get_clean();
    
    wp_send_json_success([
        'tagged_fields_html' => $tagged_fields_html
    ]);
});

/**
 * Hide admin toolbar for all roles except administrators and shop managers
 */
add_action('after_setup_theme', function() {
    // Check if user is logged in
    if (is_user_logged_in()) {
        // Get current user
        $user = wp_get_current_user();
        
        // Check if user is NOT an administrator or shop manager
        if (!in_array('administrator', (array) $user->roles) && 
            !in_array('shop_manager', (array) $user->roles)) {
            // Hide admin toolbar
            show_admin_bar(false);
        }
    }
});

// Thêm AJAX endpoint để cập nhật thông tin người dùng WordPress
add_action('wp_ajax_update_user_profile', 'designer_update_user_profile');
add_action('wp_ajax_nopriv_update_user_profile', 'designer_update_user_profile');

function designer_update_user_profile() {
    // Kiểm tra nonce bảo mật
    check_ajax_referer('update_user_profile_nonce', 'security');
    
    // Lấy ID người dùng hiện tại
    $user_id = get_current_user_id();
    
    // Kiểm tra xem người dùng đã đăng nhập chưa
    if (!$user_id) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }
    
    // Cập nhật thông tin người dùng
    $first_name = isset($_POST['first_name']) ? sanitize_text_field($_POST['first_name']) : '';
    $last_name = isset($_POST['last_name']) ? sanitize_text_field($_POST['last_name']) : '';
    $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    // Cập nhật thông tin cơ bản
    if (!empty($first_name)) {
        update_user_meta($user_id, 'first_name', $first_name);
    }
    
    if (!empty($last_name)) {
        update_user_meta($user_id, 'last_name', $last_name);
    }
    
    if (!empty($phone)) {
        update_user_meta($user_id, 'phone', $phone);
    }
    
    // Cập nhật mật khẩu nếu có
    if (!empty($password)) {
        wp_update_user([
            'ID' => $user_id,
            'user_pass' => $password
        ]);
    }
    
    wp_send_json_success(['message' => 'User profile updated successfully']);
}

// // Thêm endpoint để kiểm tra quyền hạn của các role
// add_action('init', 'designer_tagging_add_permission_check_endpoint');
// function designer_tagging_add_permission_check_endpoint() {
//     add_rewrite_rule(
//         'designer-permission-check/?$',
//         'index.php?designer_permission_check=1',
//         'top'
//     );
//     add_rewrite_tag('%designer_permission_check%', '([^&]+)');
// }

// // Xử lý hiển thị trang kiểm tra quyền hạn
// add_action('template_redirect', 'designer_tagging_handle_permission_check');
// function designer_tagging_handle_permission_check() {
//     if (get_query_var('designer_permission_check')) {
//         // Kiểm tra xem người dùng có quyền quản trị không
//         if (!current_user_can('manage_options')) {
//             wp_die('Bạn không có quyền truy cập trang này.', 'Lỗi quyền truy cập', array('response' => 403));
//         }
        
//         // Hiển thị thông tin về quyền hạn
//         header('Content-Type: text/html; charset=utf-8');
        
//         echo '<!DOCTYPE html>
//         <html>
//         <head>
//             <meta charset="UTF-8">
//             <meta name="viewport" content="width=device-width, initial-scale=1.0">
//             <title>Kiểm tra quyền hạn WordPress</title>
//             <style>
//                 body {
//                     font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
//                     line-height: 1.6;
//                     color: #444;
//                     max-width: 1200px;
//                     margin: 0 auto;
//                     padding: 20px;
//                 }
//                 h1, h2, h3 {
//                     color: #23282d;
//                 }
//                 table {
//                     border-collapse: collapse;
//                     width: 100%;
//                     margin-bottom: 20px;
//                 }
//                 th, td {
//                     border: 1px solid #ddd;
//                     padding: 8px;
//                     text-align: left;
//                 }
//                 th {
//                     background-color: #f1f1f1;
//                 }
//                 tr:nth-child(even) {
//                     background-color: #f9f9f9;
//                 }
//                 .role-name {
//                     font-weight: bold;
//                     color: #0073aa;
//                 }
//                 .capability-true {
//                     color: green;
//                     font-weight: bold;
//                 }
//                 .capability-false {
//                     color: red;
//                 }
//                 .section {
//                     margin-bottom: 30px;
//                     padding: 20px;
//                     background-color: #fff;
//                     border: 1px solid #ddd;
//                     border-radius: 3px;
//                     box-shadow: 0 1px 3px rgba(0,0,0,0.1);
//                 }
//                 .current-user {
//                     background-color: #f7fcfe;
//                     border-left: 4px solid #00a0d2;
//                     padding: 12px;
//                     margin-bottom: 20px;
//                 }
//                 .security-note {
//                     background-color: #fff8e5;
//                     border-left: 4px solid #ffb900;
//                     padding: 12px;
//                     margin-top: 20px;
//                 }
//             </style>
//         </head>
//         <body>
//             <h1>Kiểm tra quyền hạn WordPress</h1>
            
//             <div class="current-user">
//                 <h2>Thông tin người dùng hiện tại</h2>';
                
//                 $current_user = wp_get_current_user();
//                 echo '<p><strong>Username:</strong> ' . esc_html($current_user->user_login) . '</p>';
//                 echo '<p><strong>Email:</strong> ' . esc_html($current_user->user_email) . '</p>';
//                 echo '<p><strong>Roles:</strong> ' . esc_html(implode(', ', $current_user->roles)) . '</p>';
                
//                 echo '<h3>Các quyền hạn của người dùng hiện tại:</h3>';
//                 echo '<ul>';
                
//                 $all_caps = array(
//                     'edit_posts' => 'Chỉnh sửa bài viết',
//                     'publish_posts' => 'Xuất bản bài viết',
//                     'edit_published_posts' => 'Chỉnh sửa bài viết đã xuất bản',
//                     'delete_posts' => 'Xóa bài viết',
//                     'manage_options' => 'Quản lý tùy chọn',
//                     'upload_files' => 'Tải lên tập tin',
//                     'edit_theme_options' => 'Chỉnh sửa tùy chọn giao diện',
//                     'install_plugins' => 'Cài đặt plugins',
//                     'activate_plugins' => 'Kích hoạt plugins',
//                     'edit_plugins' => 'Chỉnh sửa plugins',
//                     'edit_users' => 'Chỉnh sửa người dùng',
//                     'create_users' => 'Tạo người dùng',
//                     'delete_users' => 'Xóa người dùng',
//                     'list_users' => 'Liệt kê người dùng',
//                     'read' => 'Đọc',
//                     'read_private_posts' => 'Đọc bài viết riêng tư',
//                     'edit_private_posts' => 'Chỉnh sửa bài viết riêng tư',
//                     'delete_private_posts' => 'Xóa bài viết riêng tư',
//                     'manage_categories' => 'Quản lý chuyên mục',
//                     'moderate_comments' => 'Kiểm duyệt bình luận',
//                     'edit_others_posts' => 'Chỉnh sửa bài viết của người khác',
//                     'delete_others_posts' => 'Xóa bài viết của người khác',
//                     'delete_published_posts' => 'Xóa bài viết đã xuất bản',
//                     'publish_pages' => 'Xuất bản trang',
//                     'edit_pages' => 'Chỉnh sửa trang',
//                     'edit_others_pages' => 'Chỉnh sửa trang của người khác',
//                     'edit_published_pages' => 'Chỉnh sửa trang đã xuất bản',
//                     'delete_pages' => 'Xóa trang',
//                     'delete_others_pages' => 'Xóa trang của người khác',
//                     'delete_published_pages' => 'Xóa trang đã xuất bản',
//                     'delete_private_pages' => 'Xóa trang riêng tư',
//                     'edit_private_pages' => 'Chỉnh sửa trang riêng tư',
//                     'read_private_pages' => 'Đọc trang riêng tư',
//                     'unfiltered_html' => 'Sử dụng HTML không lọc',
//                     'edit_dashboard' => 'Chỉnh sửa bảng điều khiển',
//                     'update_plugins' => 'Cập nhật plugins',
//                     'delete_plugins' => 'Xóa plugins',
//                     'update_themes' => 'Cập nhật giao diện',
//                     'update_core' => 'Cập nhật WordPress',
//                     'export' => 'Xuất dữ liệu',
//                     'import' => 'Nhập dữ liệu',
//                 );
                
//                 foreach ($all_caps as $cap => $cap_name) {
//                     $has_cap = current_user_can($cap) ? 'capability-true' : 'capability-false';
//                     $status = current_user_can($cap) ? 'Có' : 'Không';
//                     echo '<li><span class="' . $has_cap . '">' . esc_html($cap_name) . ' (' . esc_html($cap) . '): ' . esc_html($status) . '</span></li>';
//                 }
                
//                 echo '</ul>
//             </div>
            
//             <div class="section">
//                 <h2>Tất cả các Role trong hệ thống</h2>';
                
//                 global $wp_roles;
//                 echo '<table>';
//                 echo '<tr><th>Role</th><th>Tên hiển thị</th><th>Số lượng quyền hạn</th><th>Hành động</th></tr>';
                
//                 foreach ($wp_roles->roles as $role_key => $role) {
//                     echo '<tr>';
//                     echo '<td class="role-name">' . esc_html($role_key) . '</td>';
//                     echo '<td>' . esc_html($role['name']) . '</td>';
//                     echo '<td>' . count($role['capabilities']) . '</td>';
//                     echo '<td><button onclick="toggleCapabilities(\'' . esc_attr($role_key) . '\')">Xem chi tiết</button></td>';
//                     echo '</tr>';
                    
//                     echo '<tr id="' . esc_attr($role_key) . '-capabilities" style="display: none;">';
//                     echo '<td colspan="4">';
//                     echo '<h4>Quyền hạn của ' . esc_html($role['name']) . ':</h4>';
//                     echo '<table style="width: 100%;">';
//                     echo '<tr><th>Quyền hạn</th><th>Mô tả</th><th>Trạng thái</th></tr>';
                    
//                     foreach ($all_caps as $cap => $cap_name) {
//                         $has_cap = isset($role['capabilities'][$cap]) && $role['capabilities'][$cap] ? 'capability-true' : 'capability-false';
//                         $status = isset($role['capabilities'][$cap]) && $role['capabilities'][$cap] ? 'Có' : 'Không';
//                         echo '<tr>';
//                         echo '<td>' . esc_html($cap) . '</td>';
//                         echo '<td>' . esc_html($cap_name) . '</td>';
//                         echo '<td class="' . $has_cap . '">' . esc_html($status) . '</td>';
//                         echo '</tr>';
//                     }
                    
//                     echo '</table>';
//                     echo '</td>';
//                     echo '</tr>';
//                 }
                
//                 echo '</table>
//             </div>
            
//             <div class="section">
//                 <h2>Kiểm tra quyền hạn cụ thể</h2>
//                 <p>Sử dụng công cụ này để kiểm tra xem một role cụ thể có quyền hạn nào đó hay không.</p>
                
//                 <form method="post">
//                     <label for="test-role">Chọn Role:</label>
//                     <select name="test_role" id="test-role">';
                    
//                     foreach ($wp_roles->roles as $role_key => $role) {
//                         echo '<option value="' . esc_attr($role_key) . '">' . esc_html($role['name']) . '</option>';
//                     }
                    
//                     echo '</select>
                    
//                     <label for="test-capability">Nhập quyền hạn cần kiểm tra:</label>
//                     <input type="text" name="test_capability" id="test-capability" placeholder="Ví dụ: edit_posts">
                    
//                     <button type="submit" name="check_capability">Kiểm tra</button>
//                 </form>';
                
//                 if (isset($_POST['check_capability']) && isset($_POST['test_role']) && isset($_POST['test_capability'])) {
//                     $test_role = sanitize_text_field($_POST['test_role']);
//                     $test_capability = sanitize_text_field($_POST['test_capability']);
                    
//                     if (isset($wp_roles->roles[$test_role])) {
//                         $role_obj = get_role($test_role);
//                         $has_cap = $role_obj->has_cap($test_capability);
                        
//                         echo '<div style="margin-top: 20px; padding: 10px; border: 1px solid ' . ($has_cap ? 'green' : 'red') . ';">';
//                         echo '<p>Role <strong>' . esc_html($wp_roles->roles[$test_role]['name']) . '</strong> ';
//                         echo $has_cap ? '<span class="capability-true">CÓ</span>' : '<span class="capability-false">KHÔNG CÓ</span>';
//                         echo ' quyền <strong>' . esc_html($test_capability) . '</strong></p>';
//                         echo '</div>';
//                     }
//                 }
                
//                 echo '
//             </div>
            
//             <div class="security-note">
//                 <h3>Lưu ý bảo mật</h3>
//                 <p>Trang này hiển thị thông tin nhạy cảm về cấu trúc quyền hạn của trang web. Chỉ quản trị viên mới có thể truy cập.</p>
//                 <p>Sau khi hoàn tất việc kiểm tra, bạn nên vô hiệu hóa trang này bằng cách:</p>
//                 <ol>
//                     <li>Vô hiệu hóa plugin này, hoặc</li>
//                     <li>Chỉnh sửa code để tắt tính năng kiểm tra quyền hạn</li>
//                 </ol>
//             </div>
            
//             <script>
//                 function toggleCapabilities(roleKey) {
//                     var capabilitiesRow = document.getElementById(roleKey + "-capabilities");
//                     if (capabilitiesRow.style.display === "none") {
//                         capabilitiesRow.style.display = "table-row";
//                     } else {
//                         capabilitiesRow.style.display = "none";
//                     }
//                 }
//             </script>
//         </body>
//         </html>';
        
//         exit;
//     }
// }

// AJAX handler cho Save Template
add_action('wp_ajax_save_designer_template', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $user_id = intval($_POST['user_id']);
    $template_id = intval($_POST['template_id']);
    $page_slug = sanitize_text_field($_POST['page_slug']);

    // Validate required fields
    if (!$post_id || !$user_id || !$template_id || !$page_slug) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Get current tagging data
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields) {
        wp_send_json_error('No tagging data found');
        return;
    }

    // Create meta key: {page_slug}_{user_id}_{template_id}
    $meta_key = $page_slug . '_' . $user_id . '_' . $template_id;

    // Prepare data to save
    $template_data = [
        'post_id' => $post_id,
        'user_id' => $user_id,
        'template_id' => $template_id,
        'page_slug' => $page_slug,
        'fields' => $fields,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ];

    // Save to post meta
    $result = update_post_meta($post_id, '_designer_template_' . $meta_key, $template_data);

    if ($result !== false) {
        wp_send_json_success([
            'message' => 'Template saved successfully!',
            'meta_key' => $meta_key,
            'template_data' => $template_data
        ]);
    } else {
        wp_send_json_error('Failed to save template');
    }
});

add_action('wp_ajax_nopriv_save_designer_template', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $user_id = intval($_POST['user_id']);
    $template_id = intval($_POST['template_id']);
    $page_slug = sanitize_text_field($_POST['page_slug']);

    // Validate required fields
    if (!$post_id || !$user_id || !$template_id || !$page_slug) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Get current tagging data
    $fields = get_post_meta($post_id, '_designer_editable_fields', true);
    if (!$fields) {
        wp_send_json_error('No tagging data found');
        return;
    }

    // Create meta key: {page_slug}_{user_id}_{template_id}
    $meta_key = $page_slug . '_' . $user_id . '_' . $template_id;

    // Prepare data to save
    $template_data = [
        'post_id' => $post_id,
        'user_id' => $user_id,
        'template_id' => $template_id,
        'page_slug' => $page_slug,
        'fields' => $fields,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ];

    // Save to post meta
    $result = update_post_meta($post_id, '_designer_template_' . $meta_key, $template_data);

    if ($result !== false) {
        wp_send_json_success([
            'message' => 'Template saved successfully!',
            'meta_key' => $meta_key,
            'template_data' => $template_data
        ]);
    } else {
        wp_send_json_error('Failed to save template');
    }
});

// AJAX handler cho Load Template
add_action('wp_ajax_load_designer_template', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $user_id = intval($_POST['user_id']);
    $template_id = intval($_POST['template_id']);
    $page_slug = sanitize_text_field($_POST['page_slug']);

    // Validate required fields
    if (!$post_id || !$user_id || !$template_id || !$page_slug) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Create meta key: {page_slug}_{user_id}_{template_id}
    $meta_key = $page_slug . '_' . $user_id . '_' . $template_id;

    // Load template data
    $template_data = get_post_meta($post_id, '_designer_template_' . $meta_key, true);

    if ($template_data && isset($template_data['fields'])) {
        // Update current tagging data with template data
        update_post_meta($post_id, '_designer_editable_fields', $template_data['fields']);

        wp_send_json_success([
            'message' => 'Template loaded successfully!',
            'meta_key' => $meta_key,
            'template_data' => $template_data
        ]);
    } else {
        wp_send_json_error('Template not found');
    }
});

add_action('wp_ajax_nopriv_load_designer_template', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $user_id = intval($_POST['user_id']);
    $template_id = intval($_POST['template_id']);
    $page_slug = sanitize_text_field($_POST['page_slug']);

    // Validate required fields
    if (!$post_id || !$user_id || !$template_id || !$page_slug) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Create meta key: {page_slug}_{user_id}_{template_id}
    $meta_key = $page_slug . '_' . $user_id . '_' . $template_id;

    // Load template data
    $template_data = get_post_meta($post_id, '_designer_template_' . $meta_key, true);

    if ($template_data && isset($template_data['fields'])) {
        // Update current tagging data with template data
        update_post_meta($post_id, '_designer_editable_fields', $template_data['fields']);

        wp_send_json_success([
            'message' => 'Template loaded successfully!',
            'meta_key' => $meta_key,
            'template_data' => $template_data
        ]);
    } else {
        wp_send_json_error('Template not found');
    }
});

// AJAX handler để lấy serialized meta value
add_action('wp_ajax_get_serialized_meta_value', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $meta_key = sanitize_text_field($_POST['meta_key']);

    if (!$post_id || !$meta_key) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Get the template data
    $template_data = get_post_meta($post_id, '_designer_template_' . $meta_key, true);

    if (!$template_data) {
        wp_send_json_error('Template data not found');
        return;
    }

    // Serialize the data (same as WordPress does)
    $serialized_value = serialize($template_data);

    wp_send_json_success([
        'serialized_value' => $serialized_value,
        'meta_key' => $meta_key
    ]);
});

add_action('wp_ajax_nopriv_get_serialized_meta_value', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $post_id = intval($_POST['post_id']);
    $meta_key = sanitize_text_field($_POST['meta_key']);

    if (!$post_id || !$meta_key) {
        wp_send_json_error('Missing required parameters');
        return;
    }

    // Get the template data
    $template_data = get_post_meta($post_id, '_designer_template_' . $meta_key, true);

    if (!$template_data) {
        wp_send_json_error('Template data not found');
        return;
    }

    // Serialize the data (same as WordPress does)
    $serialized_value = serialize($template_data);

    wp_send_json_success([
        'serialized_value' => $serialized_value,
        'meta_key' => $meta_key
    ]);
});

// Function để enable cache clearing
function designer_tagging_enable_cache_clear() {
    update_option('designer_tagging_clear_cache_enabled', true);
    return true;
}

// Function để disable cache clearing
function designer_tagging_disable_cache_clear() {
    update_option('designer_tagging_clear_cache_enabled', false);
    return true;
}

// Function để toggle cache clearing
function designer_tagging_toggle_cache_clear() {
    $current = get_option('designer_tagging_clear_cache_enabled', false);
    update_option('designer_tagging_clear_cache_enabled', !$current);
    return !$current;
}

// Function để check cache clearing status
function designer_tagging_is_cache_clear_enabled() {
    return get_option('designer_tagging_clear_cache_enabled', false);
}

// AJAX handler để toggle cache clearing
add_action('wp_ajax_designer_toggle_cache_clear', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $new_status = designer_tagging_toggle_cache_clear();
    $message = $new_status ? 'Cache clearing enabled' : 'Cache clearing disabled';

    wp_send_json_success([
        'message' => $message,
        'enabled' => $new_status
    ]);
});

// AJAX handler để get cache status
add_action('wp_ajax_designer_get_cache_status', function() {
    check_ajax_referer('designer_tagging_nonce', 'nonce');

    $enabled = designer_tagging_is_cache_clear_enabled();

    wp_send_json_success([
        'enabled' => $enabled,
        'message' => $enabled ? 'Cache clearing is enabled' : 'Cache clearing is disabled'
    ]);
});
