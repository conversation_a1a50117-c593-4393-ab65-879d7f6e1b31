<?php

/**
 * <PERSON><PERSON><PERSON> request GraphQL và trả về dữ liệu
 *
 * @param string $query Câu truy vấn GraphQL
 * @param array $variables (T<PERSON><PERSON> chọn) Biến truyền vào query
 * @return array|WP_Error
 */
function ipt_home_fetch_graphql_data($query, $variables = array()) {
    $url = GRAPHQL_API_URL;

    $body = array(
        'query' => $query,
        'variables' => (object) $variables,
    );

    // Thêm Bearer token vào header cho tất cả GraphQL requests
    $headers = array(
        'Content-Type' => 'application/json',
    );

    // Check if this is a webhook-related mutation/query for token prefix
    $webhook_patterns = ['webhooks_', 'Webhooks_', 'webhook_', 'Webhook_'];
    $is_webhooks_request = false;

    foreach ($webhook_patterns as $pattern) {
        if (strpos($query, $pattern) !== false) {
            $is_webhooks_request = true;
            error_log("GraphQL: Found webhook pattern '{$pattern}' in query");
            break;
        }
    }

    // Add Bearer token for ALL GraphQL requests
    if (defined('GRAPHQL_TOKEN') && GRAPHQL_TOKEN) {
        if ($is_webhooks_request) {
            // Add webhooks_ prefix for webhook APIs
            $token = GRAPHQL_TOKEN;
            error_log('GraphQL: Adding Bearer token with webhooks_ prefix');
        } else {
            // Use token as-is for non-webhook APIs
            $token = GRAPHQL_TOKEN;
            error_log('GraphQL: Adding Bearer token without prefix');
        }

        $headers['Authorization'] = 'Bearer ' . $token;
        error_log('GraphQL: Original token: ' . substr(GRAPHQL_TOKEN, 0, 10) . '...');
        error_log('GraphQL: Final token: ' . substr($token, 0, 20) . '...');
    } else {
        error_log('GraphQL: GRAPHQL_TOKEN not defined or empty - NO BEARER TOKEN ADDED');
    }

    // Debug: Log the final headers being sent
    error_log('GraphQL: ===== REQUEST DEBUG =====');
    error_log('GraphQL: URL: ' . $url);
    error_log('GraphQL: Query contains: ' . substr($query, 0, 100) . '...');
    error_log('GraphQL: Final headers: ' . print_r($headers, true));
    error_log('GraphQL: Request body: ' . json_encode($body));
    error_log('GraphQL: ===== END DEBUG =====');

    $response = wp_remote_post($url, array(
        'headers' => $headers,
        'body' => json_encode($body),
        'timeout' => 15,
    ));

    if (is_wp_error($response)) {
        return $response;
    }

    $data = json_decode(wp_remote_retrieve_body($response), true);

    return $data;
}

add_action('wp_ajax_ipt_home_graphql', 'ipt_home_graphql_ajax_handler');
add_action('wp_ajax_nopriv_ipt_home_graphql', 'ipt_home_graphql_ajax_handler');

function ipt_home_graphql_ajax_handler() {
    $query = isset($_POST['query']) ? $_POST['query'] : '';
    $variables = isset($_POST['variables']) ? $_POST['variables'] : array();

    // Nếu là chuỗi rỗng hoặc không phải mảng/object thì gán lại là mảng rỗng
    if (empty($variables) || $variables === '' || $variables === 'null') {
        $variables = array();
    } elseif (is_string($variables)) {
        $variables = json_decode(stripslashes($variables), true);
        if (!is_array($variables)) {
            $variables = array();
        }
    }

    $result = ipt_home_fetch_graphql_data($query, $variables);

    wp_send_json($result);
}
