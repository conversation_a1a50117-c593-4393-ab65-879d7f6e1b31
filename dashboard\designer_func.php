<?php

/**
 * Check if user is logged in and has required role
 * 
 * @param array $allowed_roles Array of allowed roles
 * @return bool True if user is logged in and has required role, false otherwise
 */
function ipt_designer_check_auth($allowed_roles = array('designer')) {
    // Check if user is logged in
    if (!is_user_logged_in()) {
        return false;
    }
    
    // If no specific roles required, just being logged in is enough
    if (empty($allowed_roles)) {
        return true;
    }
    
    // Get current user
    $user = wp_get_current_user();
    
    // Check if user has any of the allowed roles
    foreach ($allowed_roles as $role) {
        if (in_array($role, (array) $user->roles)) {
            return true;
        }
    }
    
    return false;
}

// Thêm rewrite rule cho slug /designer
add_action('init', function() {
    // Route chính cho /designer
    add_rewrite_rule('^designer/?$', 'index.php?designer_page=designer_index', 'top');
    
    // Các route con cho designer
    add_rewrite_rule('^designer/create-template/?$', 'index.php?designer_page=create_template', 'top');
    add_rewrite_rule('^designer/manage-templates/?$', 'index.php?designer_page=manage_templates', 'top');
    add_rewrite_rule('^designer/info/?$', 'index.php?designer_page=info', 'top');
    add_rewrite_rule('^designer/login/?$', 'index.php?designer_page=designer_login', 'top');
});

// Đăng ký query var mới
add_filter('query_vars', function($vars) {
    $vars[] = 'designer_page';
    return $vars;
});

// Điều hướng tới template khi truy cập /designer
add_action('template_redirect', function() {
    $page = get_query_var('designer_page');
    if ($page) {
        // Xử lý route chính /designer
        if ($page === 'designer_index') {
            // Kiểm tra đăng nhập
            if (!is_user_logged_in()) {
                // Chưa đăng nhập, chuyển hướng đến trang đăng nhập
                wp_redirect(home_url('/designer/login'));
                exit;
            }
            
            // Đã đăng nhập, kiểm tra role
            $user = wp_get_current_user();
            
            if (in_array('designer', (array) $user->roles)) {
                // Người dùng có role designer, chuyển hướng đến manage-templates
                wp_redirect(home_url('/designer/manage-templates'));
                exit;
            } else {
                // Các role khác, chuyển hướng về trang chủ
                wp_redirect(home_url());
                exit;
            }
        }
        
        // Danh sách các trang cần đăng nhập để truy cập
        $protected_pages = array(
            'create_template',
            'manage_templates',
            'info'
        );
        
        // Kiểm tra xem trang hiện tại có cần đăng nhập không
        if (in_array($page, $protected_pages)) {
            // Kiểm tra đăng nhập và quyền truy cập
            if (!ipt_designer_check_auth(array('designer', 'administrator', 'shop_manager'))) {
                // Nếu chưa đăng nhập, chuyển hướng đến trang đăng nhập
                wp_redirect(home_url('/designer/login'));
                exit;
            }
        }
        
        // Sử dụng đường dẫn file hệ thống, không phải URL
        $template = plugin_dir_path(dirname(__FILE__)) . 'dashboard/' . $page . '.php';
        
        // Debug để xem đường dẫn file
        error_log('Template path: ' . $template);
        
        if (file_exists($template)) {
            include $template;
            exit;
        } else {
            // Nếu không có file, trả về 404
            global $wp_query;
            $wp_query->set_404();
            status_header(404);
            nocache_headers();
            include get_query_template('404');
            exit;
        }
    }
});

/**
 * Handle login via Ajax
 */
function ipt_designer_login() {
    // Check if nonce exists before verifying
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_login_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed. Please refresh the page and try again.'));
        return;
    }
    
    // Get login information
    $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $designer_id = isset($_POST['designer_id']) ? sanitize_text_field($_POST['designer_id']) : '';

    // Check email
    if (empty($email)) {
        wp_send_json_error(array('message' => 'Please enter your email.'));
        return;
    }

    // Check password
    if (empty($password)) {
        wp_send_json_error(array('message' => 'Please enter your password.'));
        return;
    }

    // Check designer_id (should be provided from GraphQL response)
    if (empty($designer_id)) {
        wp_send_json_error(array('message' => 'Designer ID is required.'));
        return;
    }
    
    // Get user by email
    $user = get_user_by('email', $email);
    
    // If user not found, try by username
    if (!$user) {
        $user = get_user_by('login', $email);
    }
    
    // Check user and password
    if (!$user || !wp_check_password($password, $user->user_pass, $user->ID)) {
        wp_send_json_error(array('message' => 'Invalid email or password.'));
        return;
    }
    
    // Check if user is an administrator (block administrators from using this login form)
    if (in_array('administrator', (array) $user->roles)) {
        wp_send_json_error(array('message' => 'Administrators should use the WordPress admin login page.'));
        return;
    }
    
    // All other user roles are allowed to login (customer, subscriber, etc.)
    
    // Use WordPress core function to log in the user
    $user_signon = wp_signon(
        array(
            'user_login'    => $user->user_login,
            'user_password' => $password,
            'remember'      => true
        ),
        is_ssl()
    );
    
    if (is_wp_error($user_signon)) {
        wp_send_json_error(array('message' => $user_signon->get_error_message()));
        return;
    }
    
    // Set auth cookie and current user
    wp_set_auth_cookie($user->ID, true);
    wp_set_current_user($user->ID);

    // Save designer_id as user meta
    $meta_saved = update_user_meta($user->ID, 'designer_id', $designer_id);

    // Log the meta save operation
    error_log("Designer Login: Saving designer_id {$designer_id} for user {$user->ID}. Result: " . ($meta_saved ? 'success' : 'failed'));

    // Determine redirect URL based on user role
    if (in_array('designer', (array) $user->roles)) {
        // Designer role - redirect to designer dashboard
        $redirect_url = apply_filters('login_redirect', home_url() . '/designer/manage-templates', '', $user);
    } elseif (in_array('administrator', (array) $user->roles) || in_array('shop_manager', (array) $user->roles)) {
        // Admin or Shop Manager - redirect to WordPress admin
        $redirect_url = apply_filters('login_redirect', admin_url(), '', $user);
    } else {
        // Other roles - default redirect
        $redirect_url = apply_filters('login_redirect', home_url(), '', $user);
    }

    // Return success result
    wp_send_json_success(array(
        'message' => 'Login successful.',
        'redirect' => $redirect_url,
        'designer_id' => $designer_id,
        'user_id' => $user->ID
    ));
}
add_action('wp_ajax_nopriv_ipt_designer_login', 'ipt_designer_login');
add_action('wp_ajax_ipt_designer_login', 'ipt_designer_login');

/**
 * Get cache buster version for assets
 *
 * @param string $file_path Path to the file
 * @return string Version string for cache busting
 */
function get_asset_version($file_path = '') {
    if (!empty($file_path) && file_exists($file_path)) {
        // Use file modification time for specific files
        return filemtime($file_path);
    }

    // Fallback to current timestamp for development
    // In production, you might want to use plugin version or build number
    return time();
}

/**
 * Generate asset URL with cache busting
 *
 * @param string $asset_url Base URL of the asset
 * @param string $file_path Optional file path for filemtime versioning
 * @return string Asset URL with version parameter
 */
function get_versioned_asset_url($asset_url, $file_path = '') {
    $version = get_asset_version($file_path);
    $separator = (strpos($asset_url, '?') !== false) ? '&' : '?';
    return $asset_url . $separator . 'v=' . $version;
}
