/**
 * Backup and Approval Process Handler
 * Handles asynchronous backup and approval process with status polling
 */

class BackupApprovalHandler {
    constructor() {
        this.pollInterval = null;
        this.maxPollTime = 15 * 60 * 1000; // 15 minutes max polling
        this.pollFrequency = 5000; // Poll every 5 seconds
        this.startTime = null;
    }

    /**
     * Start the backup and approval process
     */
    async startProcess(formData) {
        try {
            console.log('Starting backup and approval process...');
            
            // Show loading state
            this.showLoadingState();
            
            // Make the initial request
            const response = await fetch(ajaxurl, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                console.log('Process initiated:', result.data);
                this.startStatusPolling(result.data.process_id, formData.get('security'));
            } else {
                console.error('Failed to start process:', result.data);
                this.showError('Failed to start backup process: ' + result.data.message);
            }
        } catch (error) {
            console.error('Error starting process:', error);
            this.showError('Network error occurred while starting the process');
        }
    }

    /**
     * Start polling for status updates
     */
    startStatusPolling(processId, security) {
        this.startTime = Date.now();
        
        const pollStatus = async () => {
            try {
                // Check if we've exceeded max poll time
                if (Date.now() - this.startTime > this.maxPollTime) {
                    this.stopPolling();
                    this.showError('Process timeout - please check server logs');
                    return;
                }

                const formData = new FormData();
                formData.append('action', 'ipt_check_backup_approval_status');
                formData.append('process_id', processId);
                formData.append('security', security);

                const response = await fetch(ajaxurl, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    this.updateStatus(result.data);
                    
                    // Check if process is complete
                    if (result.data.status === 'completed') {
                        this.stopPolling();
                        this.showSuccess(result.data);
                    } else if (result.data.status === 'failed') {
                        this.stopPolling();
                        this.showError('Process failed: ' + result.data.error);
                    }
                } else {
                    console.error('Status check failed:', result.data);
                    // Continue polling unless it's a critical error
                    if (result.data === 'Process not found or expired') {
                        this.stopPolling();
                        this.showError('Process expired or not found');
                    }
                }
            } catch (error) {
                console.error('Error checking status:', error);
                // Continue polling on network errors
            }
        };

        // Start polling
        this.pollInterval = setInterval(pollStatus, this.pollFrequency);
        
        // Initial status check
        pollStatus();
    }

    /**
     * Stop status polling
     */
    stopPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
    }

    /**
     * Update the UI with current status
     */
    updateStatus(statusData) {
        console.log('Status update:', statusData);
        
        // Update progress bar if exists
        const progressBar = document.querySelector('#backup-progress-bar');
        if (progressBar) {
            progressBar.style.width = statusData.progress + '%';
            progressBar.textContent = statusData.progress + '%';
        }

        // Update status text
        const statusText = document.querySelector('#backup-status-text');
        if (statusText) {
            const stepMessages = {
                'starting': 'Initializing process...',
                'api_login': 'Logging into API service...',
                'creating_backup': 'Creating backup (this may take several minutes)...',
                'processing_approval': 'Processing approval...',
                'finished': 'Process completed successfully!'
            };
            
            statusText.textContent = stepMessages[statusData.step] || statusData.step;
        }

        // Update elapsed time
        const timeText = document.querySelector('#backup-elapsed-time');
        if (timeText) {
            timeText.textContent = 'Elapsed: ' + statusData.elapsed_time;
        }
    }

    /**
     * Show loading state
     */
    showLoadingState() {
        // Create or show progress modal
        let modal = document.querySelector('#backup-progress-modal');
        if (!modal) {
            modal = this.createProgressModal();
            document.body.appendChild(modal);
        }
        
        modal.style.display = 'block';
        
        // Disable the trigger button
        const triggerButton = document.querySelector('[data-action="backup-approval"]');
        if (triggerButton) {
            triggerButton.disabled = true;
            triggerButton.textContent = 'Processing...';
        }
    }

    /**
     * Create progress modal
     */
    createProgressModal() {
        const modal = document.createElement('div');
        modal.id = 'backup-progress-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            z-index: 10000;
        `;

        modal.innerHTML = `
            <div style="
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 8px;
                min-width: 400px;
                text-align: center;
            ">
                <h3>Backup and Approval in Progress</h3>
                <div style="margin: 20px 0;">
                    <div style="background: #f0f0f0; border-radius: 10px; overflow: hidden; margin-bottom: 10px;">
                        <div id="backup-progress-bar" style="
                            background: #0073aa;
                            height: 20px;
                            width: 0%;
                            transition: width 0.3s ease;
                            color: white;
                            line-height: 20px;
                            font-size: 12px;
                        ">0%</div>
                    </div>
                    <div id="backup-status-text">Initializing...</div>
                    <div id="backup-elapsed-time" style="font-size: 12px; color: #666; margin-top: 10px;">Elapsed: 0 minutes</div>
                </div>
                <p style="font-size: 14px; color: #666;">
                    This process may take 5-10 minutes. Please do not close this window.
                </p>
            </div>
        `;

        return modal;
    }

    /**
     * Show success message
     */
    showSuccess(data) {
        console.log('Process completed successfully:', data);
        
        // Hide progress modal
        const modal = document.querySelector('#backup-progress-modal');
        if (modal) {
            modal.style.display = 'none';
        }

        // Show success message
        alert('Backup and approval process completed successfully!');
        
        // Redirect if needed
        if (data.approval_data && data.approval_data.redirect_url) {
            window.location.href = data.approval_data.redirect_url;
        } else {
            // Reload page to reflect changes
            window.location.reload();
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('Process error:', message);
        
        // Hide progress modal
        const modal = document.querySelector('#backup-progress-modal');
        if (modal) {
            modal.style.display = 'none';
        }

        // Re-enable button
        const triggerButton = document.querySelector('[data-action="backup-approval"]');
        if (triggerButton) {
            triggerButton.disabled = false;
            triggerButton.textContent = 'Approve';
        }

        // Show error
        alert('Error: ' + message);
    }
}

// Initialize handler when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.backupApprovalHandler = new BackupApprovalHandler();
    
    // Example usage - attach to approval buttons
    document.addEventListener('click', function(e) {
        if (e.target.dataset.action === 'backup-approval') {
            e.preventDefault();
            
            // Get form data from the clicked element's context
            const form = e.target.closest('form') || document.querySelector('#approval-form');
            if (form) {
                const formData = new FormData(form);
                formData.set('action', 'ipt_handle_api_backup_and_approval');
                
                window.backupApprovalHandler.startProcess(formData);
            }
        }
    });
});
