/**
 * Hybrid Backup Handler
 *
 * Handles the 3-step hybrid backup process:
 * 1. Trigger API backup creation (non-blocking)
 * 2. Poll local directory every 10 seconds for .wpress file
 * 3. Upload backup file to API when ready
 */

// Debug: Log when script loads
console.log('🔄 Hybrid Backup Handler script loaded - VERSION 1.0.1');
console.log('📋 hybridBackupConfig available:', typeof hybridBackupConfig !== 'undefined');
if (typeof hybridBackupConfig !== 'undefined') {
    console.log('📋 AJAX URL:', hybridBackupConfig.ajaxurl);
    console.log('📋 Enabled:', hybridBackupConfig.enabled);
} else {
    console.warn('⚠️ hybridBackupConfig is not available - script may not be properly localized');
}

class HybridBackupHandler {
    constructor() {
        this.processId = null;
        this.statusInterval = null;
        this.maxStatusChecks = 180; // 30 minutes max (180 * 10 seconds)
        this.statusCheckCount = 0;
        this.isProcessing = false;
    }

    /**
     * Start the hybrid backup and approval process
     */
    async startProcess(formData) {
        if (this.isProcessing) {
            console.log('Process already running, ignoring new request');
            return;
        }

        try {
            this.isProcessing = true;
            console.log('🚀 Starting hybrid backup process...');
            
            // Show initial loading state
            this.showLoadingState('Initiating backup process...');

            // Get AJAX URL from configuration with multiple fallbacks
            let ajaxUrl;
            if (typeof hybridBackupConfig !== 'undefined' && hybridBackupConfig.ajaxurl) {
                ajaxUrl = hybridBackupConfig.ajaxurl;
                console.log('📡 Using AJAX URL from config:', ajaxUrl);
            } else if (typeof ajaxurl !== 'undefined') {
                ajaxUrl = ajaxurl;
                console.log('📡 Using global ajaxurl:', ajaxUrl);
            } else {
                ajaxUrl = window.location.origin + '/wp-admin/admin-ajax.php';
                console.log('📡 Using constructed AJAX URL:', ajaxUrl);
            }

            // Make the initial request
            const response = await fetch(ajaxUrl, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ Hybrid process initiated:', result.data);
                this.processId = result.data.process_id;
                
                // Show progress UI
                this.showProgressUI();
                
                // Start status polling
                this.startStatusPolling();
                
            } else {
                console.error('❌ Failed to start hybrid process:', result.data);
                this.showError('Failed to start backup process: ' + result.data.message);
                this.isProcessing = false;
            }
        } catch (error) {
            console.error('❌ Network error starting hybrid process:', error);
            this.showError('Network error occurred while starting the process');
            this.isProcessing = false;
        }
    }

    /**
     * Start polling for process status
     */
    startStatusPolling() {
        console.log('🔍 Starting status polling for process:', this.processId);
        
        this.statusCheckCount = 0;
        
        // Poll immediately, then every 10 seconds
        this.checkStatus();
        this.statusInterval = setInterval(() => {
            this.checkStatus();
        }, 10000); // 10 seconds
    }

    /**
     * Check the current status of the backup process
     */
    async checkStatus() {
        if (!this.processId) {
            console.error('No process ID available for status check');
            return;
        }

        this.statusCheckCount++;
        
        // Check for timeout
        if (this.statusCheckCount > this.maxStatusChecks) {
            console.error('❌ Status polling timeout after', this.statusCheckCount, 'checks');
            this.stopStatusPolling();
            this.showError('Process timeout - please try again');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('action', 'ipt_check_hybrid_backup_status');
            formData.append('process_id', this.processId);

            // Get AJAX URL from configuration with multiple fallbacks
            let ajaxUrl;
            if (typeof hybridBackupConfig !== 'undefined' && hybridBackupConfig.ajaxurl) {
                ajaxUrl = hybridBackupConfig.ajaxurl;
            } else if (typeof ajaxurl !== 'undefined') {
                ajaxUrl = ajaxurl;
            } else {
                ajaxUrl = window.location.origin + '/wp-admin/admin-ajax.php';
            }

            const response = await fetch(ajaxUrl, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.updateStatus(result.data);
                
                // Check if process is complete
                if (result.data.completed) {
                    this.stopStatusPolling();
                    
                    if (result.data.status === 'completed') {
                        this.showSuccess(result.data);
                    } else {
                        this.showError('Process failed: ' + (result.data.error || 'Unknown error'));
                    }
                }
            } else {
                console.error('❌ Status check failed:', result.data);
                
                // Don't stop polling for temporary errors, but log them
                if (this.statusCheckCount % 6 === 0) { // Every minute
                    console.warn('Status check failing, but continuing to poll...');
                }
            }
        } catch (error) {
            console.error('❌ Network error during status check:', error);
            
            // Don't stop polling for network errors, but log them
            if (this.statusCheckCount % 6 === 0) { // Every minute
                console.warn('Network error during status check, but continuing to poll...');
            }
        }
    }

    /**
     * Update the UI with current status
     */
    updateStatus(statusData) {
        console.log('📊 Status update:', statusData);

        const statusElement = document.getElementById('hybrid-backup-status');
        const progressElement = document.getElementById('hybrid-backup-progress');
        const detailsElement = document.getElementById('hybrid-backup-details');

        if (!statusElement) return;

        // Update progress bar
        if (progressElement) {
            progressElement.style.width = statusData.progress + '%';
            progressElement.textContent = statusData.progress + '%';
        }

        // Update status message
        let statusMessage = this.getStatusMessage(statusData.status, statusData.step);
        statusElement.innerHTML = `
            <div class="hybrid-status-header">
                <strong>Status:</strong> ${statusMessage}
                <span class="hybrid-progress-text">(${statusData.progress}%)</span>
            </div>
        `;

        // Update details
        if (detailsElement) {
            let details = [];
            
            if (statusData.last_check) {
                details.push(`Last check: ${statusData.last_check}`);
            }
            
            if (statusData.backup_file) {
                details.push(`Backup file: ${statusData.backup_file}`);
            }
            
            if (statusData.code_file_id) {
                details.push(`Code file ID: ${statusData.code_file_id}`);
            }

            detailsElement.innerHTML = details.length > 0 ? 
                '<div class="hybrid-details">' + details.join('<br>') + '</div>' : '';
        }
    }

    /**
     * Get human-readable status message
     */
    getStatusMessage(status, step) {
        const messages = {
            'starting': '🚀 Initializing backup process...',
            'waiting_for_backup': '⏳ Backup creation triggered, waiting for file...',
            'polling': '🔍 Checking for backup file (every 10 seconds)...',
            'backup_found': '✅ Backup file found, preparing upload...',
            'upload_complete': '📤 Backup uploaded successfully, completing approval...',
            'completed': '🎉 Process completed successfully!',
            'timeout': '⏰ Timeout - backup file not created in time',
            'upload_failed': '❌ Failed to upload backup file'
        };

        return messages[status] || `Processing: ${status} (${step})`;
    }

    /**
     * Show initial loading state
     */
    showLoadingState(message = 'Processing...') {
        const container = this.getOrCreateContainer();
        container.innerHTML = `
            <div class="hybrid-backup-loading">
                <div class="hybrid-spinner"></div>
                <div class="hybrid-loading-message">${message}</div>
            </div>
        `;
        container.style.display = 'block';
    }

    /**
     * Show progress UI
     */
    showProgressUI() {
        const container = this.getOrCreateContainer();
        container.innerHTML = `
            <div class="hybrid-backup-progress-container">
                <h3>🔄 Hybrid Backup Process</h3>
                
                <div class="hybrid-progress-bar-container">
                    <div class="hybrid-progress-bar">
                        <div id="hybrid-backup-progress" class="hybrid-progress-fill">0%</div>
                    </div>
                </div>
                
                <div id="hybrid-backup-status" class="hybrid-status">
                    Initializing...
                </div>
                
                <div id="hybrid-backup-details" class="hybrid-details">
                </div>
                
                <div class="hybrid-process-info">
                    <h4>📋 Process Steps:</h4>
                    <ol>
                        <li>🚀 Trigger API backup creation (non-blocking)</li>
                        <li>🔍 Poll local directory every 10 seconds for .wpress file</li>
                        <li>📤 Upload backup file to API when ready</li>
                        <li>✅ Complete approval with code_file_id</li>
                    </ol>
                    <p><em>This process typically takes 5-15 minutes.</em></p>
                </div>
            </div>
        `;
        container.style.display = 'block';
    }

    /**
     * Show success message
     */
    showSuccess(statusData) {
        console.log('🎉 Hybrid backup process completed successfully!');
        
        const container = this.getOrCreateContainer();
        container.innerHTML = `
            <div class="hybrid-backup-success">
                <h3>✅ Backup and Approval Completed!</h3>
                <div class="hybrid-success-details">
                    <p><strong>Status:</strong> ${statusData.status}</p>
                    <p><strong>Code File ID:</strong> ${statusData.code_file_id}</p>
                    <p><strong>Backup File:</strong> ${statusData.backup_file}</p>
                    <p><strong>Process ID:</strong> ${statusData.process_id}</p>
                </div>
                <div class="hybrid-success-actions">
                    <button onclick="location.reload()" class="hybrid-btn hybrid-btn-primary">
                        🔄 Refresh Page
                    </button>
                </div>
            </div>
        `;
        
        this.isProcessing = false;
        
        // Auto-refresh after 3 seconds
        setTimeout(() => {
            location.reload();
        }, 3000);
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('❌ Hybrid backup error:', message);
        
        const container = this.getOrCreateContainer();
        container.innerHTML = `
            <div class="hybrid-backup-error">
                <h3>❌ Backup Process Failed</h3>
                <div class="hybrid-error-message">
                    <p><strong>Error:</strong> ${message}</p>
                </div>
                <div class="hybrid-error-actions">
                    <button onclick="location.reload()" class="hybrid-btn hybrid-btn-secondary">
                        🔄 Try Again
                    </button>
                </div>
            </div>
        `;
        
        this.isProcessing = false;
    }

    /**
     * Stop status polling
     */
    stopStatusPolling() {
        if (this.statusInterval) {
            clearInterval(this.statusInterval);
            this.statusInterval = null;
            console.log('🛑 Status polling stopped');
        }
    }

    /**
     * Get or create the container element
     */
    getOrCreateContainer() {
        let container = document.getElementById('hybrid-backup-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'hybrid-backup-container';
            container.className = 'hybrid-backup-container';
            
            // Insert after the approval button or at the end of body
            const approvalButton = document.querySelector('[data-action="approve"]');
            if (approvalButton && approvalButton.parentNode) {
                approvalButton.parentNode.insertBefore(container, approvalButton.nextSibling);
            } else {
                document.body.appendChild(container);
            }
        }
        return container;
    }

    /**
     * Reset the handler state
     */
    reset() {
        this.stopStatusPolling();
        this.processId = null;
        this.statusCheckCount = 0;
        this.isProcessing = false;
        
        const container = document.getElementById('hybrid-backup-container');
        if (container) {
            container.style.display = 'none';
            container.innerHTML = '';
        }
    }
}

// Initialize handler when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.hybridBackupHandler = new HybridBackupHandler();
    
    console.log('🔧 Hybrid Backup Handler initialized');
});
