<?php
/**
 * Designer Dashboard Breadcrumb
 * 
 * @param string $current_page Current page title
 * @param array $parent_pages Array of parent pages with title and link
 */
function designer_breadcrumb($current_page, $parent_pages = array()) {
    ?>
    <div class="designer-breadcrumb mb-2">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <?php foreach ($parent_pages as $parent): ?>
                <li class="breadcrumb-item">
                    <a href="<?php echo esc_url($parent['link']); ?>"><?php echo esc_html($parent['title']); ?></a>
                </li>
                <?php endforeach; ?>
                <li class="breadcrumb-item active" aria-current="page"><?php echo esc_html($current_page); ?></li>
            </ol>
        </nav>
    </div>
    <h1 class="page-title mb-4"><?php echo esc_html($current_page); ?></h1>
    <?php
}
?>

<style>
    .designer-breadcrumb {
        margin-top: 10px;
    }
    
    .designer-breadcrumb .breadcrumb {
        padding: 0;
        margin: 0;
        background-color: transparent;
    }
    
    .designer-breadcrumb .breadcrumb-item {
        font-size: 14px;
        color: #6c757d;
    }
    
    .designer-breadcrumb .breadcrumb-item a {
        color: #6c757d;
        text-decoration: none;
    }
    
    .designer-breadcrumb .breadcrumb-item a:hover {
        color: #4DCDB4;
        text-decoration: underline;
    }
    
    .designer-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
        font-size: 16px;
        line-height: 1;
        padding: 0 8px;
    }
    
    .designer-breadcrumb .breadcrumb-item.active {
        color: #4DCDB4;
    }
    
    .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-top: 5px;
    }
</style>