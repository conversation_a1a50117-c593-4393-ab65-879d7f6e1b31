/**
 * Hybrid Backup Handler Styles
 */

.hybrid-backup-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Loading State */
.hybrid-backup-loading {
    text-align: center;
    padding: 40px 20px;
}

.hybrid-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007cba;
    border-radius: 50%;
    animation: hybrid-spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes hybrid-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hybrid-loading-message {
    font-size: 16px;
    color: #666;
    font-weight: 500;
}

/* Progress UI */
.hybrid-backup-progress-container {
    padding: 20px;
}

.hybrid-backup-progress-container h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 20px;
    text-align: center;
}

.hybrid-progress-bar-container {
    margin: 20px 0;
}

.hybrid-progress-bar {
    width: 100%;
    height: 30px;
    background-color: #f0f0f0;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    border: 1px solid #ddd;
}

.hybrid-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007cba 0%, #00a0d2 100%);
    border-radius: 15px;
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
    min-width: 60px;
}

/* Status Display */
.hybrid-status {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;
    border-left: 4px solid #007cba;
}

.hybrid-status-header {
    font-size: 16px;
    line-height: 1.4;
}

.hybrid-progress-text {
    float: right;
    color: #666;
    font-weight: normal;
}

/* Details */
.hybrid-details {
    background: #fff;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin: 10px 0;
    font-size: 14px;
    color: #666;
    line-height: 1.6;
}

/* Process Info */
.hybrid-process-info {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 6px;
    margin: 20px 0;
    border: 1px solid #e0e0e0;
}

.hybrid-process-info h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.hybrid-process-info ol {
    margin: 0 0 15px 0;
    padding-left: 20px;
}

.hybrid-process-info li {
    margin: 8px 0;
    line-height: 1.4;
}

.hybrid-process-info p {
    margin: 0;
    font-style: italic;
    color: #666;
    text-align: center;
}

/* Success State */
.hybrid-backup-success {
    text-align: center;
    padding: 30px 20px;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #c3e6cb;
    border-radius: 8px;
}

.hybrid-backup-success h3 {
    margin: 0 0 20px 0;
    color: #155724;
    font-size: 24px;
}

.hybrid-success-details {
    background: rgba(255,255,255,0.8);
    padding: 20px;
    border-radius: 6px;
    margin: 20px 0;
    text-align: left;
}

.hybrid-success-details p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.hybrid-success-details strong {
    color: #155724;
    font-weight: 600;
}

/* Error State */
.hybrid-backup-error {
    text-align: center;
    padding: 30px 20px;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 1px solid #f5c6cb;
    border-radius: 8px;
}

.hybrid-backup-error h3 {
    margin: 0 0 20px 0;
    color: #721c24;
    font-size: 24px;
}

.hybrid-error-message {
    background: rgba(255,255,255,0.8);
    padding: 20px;
    border-radius: 6px;
    margin: 20px 0;
    text-align: left;
}

.hybrid-error-message p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.hybrid-error-message strong {
    color: #721c24;
    font-weight: 600;
}

/* Buttons */
.hybrid-btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 10px 5px;
}

.hybrid-btn-primary {
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    color: white;
}

.hybrid-btn-primary:hover {
    background: linear-gradient(135deg, #005a87 0%, #004666 100%);
    transform: translateY(-1px);
}

.hybrid-btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
    color: white;
}

.hybrid-btn-secondary:hover {
    background: linear-gradient(135deg, #545b62 0%, #3d4449 100%);
    transform: translateY(-1px);
}

.hybrid-success-actions,
.hybrid-error-actions {
    margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hybrid-backup-container {
        margin: 10px;
        padding: 15px;
    }
    
    .hybrid-backup-progress-container {
        padding: 15px;
    }
    
    .hybrid-backup-progress-container h3 {
        font-size: 18px;
    }
    
    .hybrid-progress-bar {
        height: 25px;
    }
    
    .hybrid-progress-fill {
        font-size: 12px;
        min-width: 50px;
    }
    
    .hybrid-process-info {
        padding: 15px;
    }
    
    .hybrid-process-info ol {
        padding-left: 15px;
    }
    
    .hybrid-btn {
        padding: 10px 20px;
        font-size: 14px;
        display: block;
        width: 100%;
        margin: 10px 0;
    }
}

/* Animation for status updates */
.hybrid-status {
    animation: hybrid-fade-in 0.3s ease;
}

@keyframes hybrid-fade-in {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Progress bar animation */
.hybrid-progress-fill {
    animation: hybrid-progress-pulse 2s ease-in-out infinite;
}

@keyframes hybrid-progress-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(0, 124, 186, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(0, 124, 186, 0);
    }
}

/* Hide container by default */
.hybrid-backup-container {
    display: none;
}

/* Show when active */
.hybrid-backup-container.active {
    display: block;
}
