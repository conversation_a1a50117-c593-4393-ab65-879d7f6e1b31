<?php
/**
 * Test Hybrid Backup Integration
 * 
 * This file tests the hybrid backup system integration with the approval button.
 * Add this shortcode to any page: [test_hybrid_integration]
 */

// Prevent direct access
if (!defined('ABSPATH')) exit;

/**
 * Test hybrid backup integration shortcode
 */
function test_hybrid_integration_shortcode() {
    // Only show to administrators
    if (!current_user_can('manage_options')) {
        return '<p>Access denied. Administrator privileges required.</p>';
    }
    
    ob_start();
    ?>
    <div style="max-width: 800px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fff;">
        <h2>🧪 Hybrid Backup Integration Test</h2>
        
        <div style="background: #f0f6fc; padding: 15px; border-left: 4px solid #0073aa; margin: 20px 0;">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li><strong>Check Console:</strong> Open browser developer tools (F12) and go to Console tab</li>
                <li><strong>Click Test Button:</strong> Click the "Test Hybrid Backup" button below</li>
                <li><strong>Watch Progress:</strong> You should see a progress UI appear</li>
                <li><strong>Monitor Logs:</strong> Check console for detailed process logs</li>
                <li><strong>Verify Integration:</strong> Ensure all components are loaded correctly</li>
            </ol>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0;">
            <h3>⚠️ Prerequisites</h3>
            <ul>
                <li>✅ Hybrid backup files uploaded to plugin directory</li>
                <li>✅ All-in-One WP Migration plugin installed</li>
                <li>✅ API endpoints configured correctly</li>
                <li>✅ WordPress cron enabled</li>
            </ul>
        </div>
        
        <!-- Test Button -->
        <div style="text-align: center; margin: 30px 0;">
            <button id="test-hybrid-backup" class="button button-primary" style="padding: 15px 30px; font-size: 16px;">
                🔄 Test Hybrid Backup Integration
            </button>
        </div>
        
        <!-- Status Display -->
        <div id="test-status" style="margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 4px; display: none;">
            <h4>📊 Test Status</h4>
            <div id="test-results"></div>
        </div>
        
        <!-- Hybrid Backup Container -->
        <div id="test-hybrid-container" class="hybrid-backup-container" style="margin: 20px 0;">
            <!-- Progress UI will appear here -->
        </div>
        
        <div style="background: #d1ecf1; padding: 15px; border-left: 4px solid #bee5eb; margin: 20px 0;">
            <h3>🔍 What This Test Checks</h3>
            <ul>
                <li><strong>JavaScript Loading:</strong> HybridBackupHandler class availability</li>
                <li><strong>CSS Styling:</strong> Progress UI appearance and animations</li>
                <li><strong>AJAX Communication:</strong> WordPress admin-ajax.php connectivity</li>
                <li><strong>Process Flow:</strong> 4-step hybrid backup process</li>
                <li><strong>Error Handling:</strong> Fallback mechanisms and error display</li>
                <li><strong>UI Integration:</strong> Container positioning and responsiveness</li>
            </ul>
        </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        console.log('🧪 Hybrid backup integration test loaded');
        
        // Check if hybrid backup components are available
        function checkHybridBackupComponents() {
            const results = [];
            
            // Check JavaScript class
            if (typeof HybridBackupHandler !== 'undefined') {
                results.push('✅ HybridBackupHandler class: Available');
            } else {
                results.push('❌ HybridBackupHandler class: Missing');
            }
            
            // Check CSS styles
            const testElement = document.createElement('div');
            testElement.className = 'hybrid-backup-container';
            document.body.appendChild(testElement);
            const styles = window.getComputedStyle(testElement);
            if (styles.display !== 'none') {
                results.push('✅ CSS styles: Loaded');
            } else {
                results.push('❌ CSS styles: Missing or not applied');
            }
            document.body.removeChild(testElement);
            
            // Check container
            const container = document.getElementById('test-hybrid-container');
            if (container) {
                results.push('✅ Container element: Found');
            } else {
                results.push('❌ Container element: Missing');
            }
            
            // Check AJAX configuration
            if (typeof hybridBackupConfig !== 'undefined') {
                results.push('✅ AJAX configuration: Available');
                results.push('📋 AJAX URL: ' + hybridBackupConfig.ajaxurl);
                results.push('📋 Poll interval: ' + (hybridBackupConfig.pollInterval / 1000) + 's');
                results.push('📋 Max attempts: ' + hybridBackupConfig.maxAttempts);
            } else {
                results.push('❌ AJAX configuration: Missing');
            }
            
            return results;
        }
        
        // Test button click handler
        $('#test-hybrid-backup').on('click', function() {
            console.log('🧪 Starting hybrid backup integration test...');
            
            const statusDiv = $('#test-status');
            const resultsDiv = $('#test-results');
            
            statusDiv.show();
            resultsDiv.html('<p>🔄 Running tests...</p>');
            
            // Run component checks
            const componentResults = checkHybridBackupComponents();
            
            let html = '<h5>Component Check Results:</h5><ul>';
            componentResults.forEach(result => {
                html += '<li>' + result + '</li>';
            });
            html += '</ul>';
            
            // Try to initialize hybrid backup handler
            try {
                const container = document.getElementById('test-hybrid-container');
                if (container && typeof HybridBackupHandler !== 'undefined') {
                    const handler = new HybridBackupHandler(container);
                    html += '<h5>✅ Handler Initialization: Success</h5>';
                    
                    // Test form data preparation
                    const testFormData = new FormData();
                    testFormData.append('action', 'ipt_handle_hybrid_backup_approval');
                    testFormData.append('approval_action', 'approve');
                    testFormData.append('template_id', '123');
                    testFormData.append('page_url', window.location.href);
                    testFormData.append('security', 'test-nonce');
                    
                    html += '<h5>📋 Test Form Data:</h5>';
                    html += '<ul>';
                    for (let [key, value] of testFormData.entries()) {
                        html += '<li><strong>' + key + ':</strong> ' + value + '</li>';
                    }
                    html += '</ul>';
                    
                    // Show test process (without actually starting it)
                    html += '<h5>🔄 Process Simulation:</h5>';
                    html += '<p><em>Click "Start Test Process" below to simulate the hybrid backup flow.</em></p>';
                    html += '<button id="start-test-process" class="button button-secondary">Start Test Process</button>';
                    
                    // Add test process handler
                    setTimeout(() => {
                        $('#start-test-process').on('click', function() {
                            console.log('🚀 Starting test process simulation...');
                            
                            // Show loading state
                            handler.showLoadingState('Testing hybrid backup system...');
                            
                            setTimeout(() => {
                                // Show progress UI
                                handler.showProgressUI();
                                handler.updateStatus('Step 1: API backup creation triggered', 25);
                                
                                setTimeout(() => {
                                    handler.updateStatus('Step 2: Polling for backup file...', 50);
                                    
                                    setTimeout(() => {
                                        handler.updateStatus('Step 3: Backup file found, uploading...', 75);
                                        
                                        setTimeout(() => {
                                            // Show success
                                            handler.showSuccess({
                                                message: 'Test completed successfully!',
                                                code_file_id: 12345,
                                                backup_file: 'test-backup.wpress',
                                                process_time: '45 seconds'
                                            });
                                        }, 2000);
                                    }, 2000);
                                }, 2000);
                            }, 1000);
                        });
                    }, 100);
                    
                } else {
                    html += '<h5>❌ Handler Initialization: Failed</h5>';
                    html += '<p>Missing container or HybridBackupHandler class</p>';
                }
            } catch (error) {
                html += '<h5>❌ Handler Initialization: Error</h5>';
                html += '<p>' + error.message + '</p>';
            }
            
            resultsDiv.html(html);
        });
        
        // Auto-run basic checks on load
        setTimeout(() => {
            $('#test-hybrid-backup').trigger('click');
        }, 1000);
    });
    </script>
    <?php
    return ob_get_clean();
}

// Register shortcode
add_shortcode('test_hybrid_integration', 'test_hybrid_integration_shortcode');

/**
 * Admin notice about test page
 */
function hybrid_integration_test_notice() {
    if (current_user_can('manage_options')) {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>🧪 Hybrid Backup Test:</strong> ';
        echo 'Add <code>[test_hybrid_integration]</code> shortcode to any page to test the integration. ';
        echo '<a href="' . admin_url('post-new.php?post_type=page') . '">Create Test Page</a></p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'hybrid_integration_test_notice');
?>
