<!DOCTYPE html>
<html lang="en">
<head>
  <title>Weaveform</title>
  <link rel="icon" type="image/x-icon" href="<?php echo plugin_dir_url(__FILE__); ?>img/logowv2.png">
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <?php
  // Include helper functions
  require_once(plugin_dir_path(__FILE__) . 'designer_func.php');

  // Dynamic versioning để clear cache
  $plugin_url = plugin_dir_url(__FILE__);
  $plugin_path = plugin_dir_path(__FILE__);
  ?>
  <link href="<?php echo get_versioned_asset_url($plugin_url . 'asset/css/bootstrap.min.css', $plugin_path . 'asset/css/bootstrap.min.css'); ?>" rel="stylesheet">
  <link rel="stylesheet" href="<?php echo get_versioned_asset_url($plugin_url . 'asset/css/font-awesome.min.css', $plugin_path . 'asset/css/font-awesome.min.css'); ?>">
  <link rel="stylesheet" type="text/css" href="<?php echo get_versioned_asset_url($plugin_url . 'asset/css/slick.css', $plugin_path . 'asset/css/slick.css'); ?>"/>
  <script src="<?php echo get_versioned_asset_url($plugin_url . 'asset/js/bootstrap.bundle.min.js', $plugin_path . 'asset/js/bootstrap.bundle.min.js'); ?>"></script>
  <script src="<?php echo get_versioned_asset_url($plugin_url . 'asset/js/jquery.min.js', $plugin_path . 'asset/js/jquery.min.js'); ?>"></script>
  <script src="<?php echo get_versioned_asset_url($plugin_url . 'asset/js/slick.min.js', $plugin_path . 'asset/js/slick.min.js'); ?>"></script>

  <!-- Localize script for AJAX -->
  <script type="text/javascript">
    var iptHomeAjax = {
      ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>'
    };
  </script>
  <style>
    * {box-sizing: border-box}
    body {font-family: "Lato", sans-serif;}
    .bg-black-web{background: #131720;}
    .user
    {
      align-items: center;
      flex-direction: row;
      display: flex;
    }
    .tab {
      float: left;
      border: 1px solid #131720;
      background-color: #131720;
      width: 250px;
      height: auto;
      padding-top: 12px;
    }
    .tab button 
    {
      display: block;
      background-color: inherit;
      color: #fff;
      padding: 12px 15px;
      width: 100%;
      border: none;
      outline: none;
      text-align: left;
      cursor: pointer;
      transition: 0.3s;
      font-size: 16px;
      border-radius: 8px;
    }
    .tab button img
    {
      margin-right: 15px;
      vertical-align: text-bottom;
    }
    .tab button:hover {
      background-color: #ffffff0d;
    }
    .tab button.active {
      background-color: #ffffff0d;
    }
    .tab button:hover:nth-child(1)
    {
      background-color: #131720;
    }
    .tabcontent {
      float: left;
      padding: 15px 30px 70px 30px;
      border: 1px solid #ccc;
      width: calc(100% - 250px);
      border-left: none;
      height: auto;
      background: #f9f9f9;
    }
    .btn-cre a
    {
      padding: 15px 20px;
      border: 1px solid rgba(216, 216, 216, 1);
      border-radius: 5px;
      text-decoration: none;
      text-transform: capitalize;
      font-weight: 600;
      color: #170000;
    }
    .btn-cre a:hover
    {
      background-color: #48c9b0;
      color: #fff;
    }
    .tabcontent .item
    {
      font-size: 16px;
      font-weight: 600;
      padding: 15px 15px;
      border-radius: 10px;
      background: #fff;
    }
    .tabcontent .item:hover
    {
      background:#f9f9f9;
    }
    .tabcontent .item .title
    {
      margin-bottom: 0.5rem;
    }
    .content-form
    {
      border-radius: 10px;
      background: #fff;
    }
    .btn-search .fa-search 
    {
        position: relative;
        left: 25px;
    }
    .btn-search input 
    {
      border-radius: 15px;
      padding: 4px 10px 3px 30px;
      border: 1px solid #D6E6FE;
    }
    .btn-search input[type="text"]
    {
      font-size:14px;
    }
    .btn-search input[type="text"]:focus
    {
      border: 1px solid #D6E6FE;
    }
    .btn-sle .fa-filter
    {
      position: relative;
      left: 25px;
    }
    #filter-template
    {
      font-size: 14px;
      width: 180px;
      padding: 5px 0 5px 18px;
      margin-left: 0px;
      border-radius: 15px;
      border: 1px solid #D6E6FE;
    }
    .content-form .title
    {
      font-size: 18px;
    }

    /* start subtab */
    .detail-content .tab-sub {
      overflow: hidden;
      position: relative;
      z-index: 10;
      margin-bottom: -2px;
    }
    .detail-content .tab-sub button {
      background-color: inherit;
      float: left;
      border: none;
      outline: none;
      cursor: pointer;
      padding: 10px 25px;
      transition: 0.3s;
      font-size: 15px;
    }
    .detail-content .tab-sub button:hover {
      background-color: #ffffff;
      border-bottom: 2px solid #48C9B0;
    }
    .detail-content .tab-sub .active-sub
    {
      border-bottom: 2px solid #48C9B0;
    }
    .detail-content .tab-sub button.active, 
    .detail-content .tab-sub .active#defaultOpensub 
    {
      background-color: #ffffff;
      border-bottom: 2px solid #48C9B0;
    }
    .detail-content .tabcontent-sub {
      display: none;
      padding: 15px 10px 25px 10px;
      border-top: 2px solid #D8D8D8;
      border-radius: 0px 0px 10px 10px;
      position: relative;
      z-index: 1;
    }
    .tabcontent-sub .content
    {
      padding: 15px;
    }
    .tabcontent-sub .content .details
    {
      margin-left: 15px;
      width: 100%;
    }
    .tabcontent-sub .details ul
    {
      padding: 0;
      list-style: none;
    }
    .tabcontent-sub .details ul li
    {
      margin-bottom:0.5rem;
      font-size: 14px;
    }
    .tabcontent-sub .content
    {
      display: flex;
      flex-direction: row;
    }
    .tabcontent-sub .content .btn-edit div
    {
      border: 1px solid #D8D8D8;
      padding: 5px 8px;
      border-radius: 5px;
    }
    .tabcontent-sub .content .btn-edit div:hover
    {
      background-color: #48C9B0;
    }
    .tabcontent-sub .content .btn-edit a
    {
      text-decoration: none;
      font-size: 14px;
      color: #170000;
      margin-left: 5px;
    }

    .drop-log .dropdown-toggle:after
    {
      position: absolute;
      content: "";
      top: 47%;
      left: calc(100% - 10px);
      -webkit-transform: translate(-50%, -50%) rotate(45deg);
      -ms-transform: translate(-50%, -50%) rotate(45deg);
      transform: translate(-50%, -50%) rotate(45deg);
      width: 10px;
      height: 10px;
      border-right: 2px solid #fff;
      border-bottom: 2px solid #fff;
      -webkit-transition: all 0.4s ease-in-out;
      -o-transition: all 0.4s ease-in-out;
      transition: all 0.4s ease-in-out;
      border-top: 0;
      border-left: 0;
    }
    .drop-log .dropdown-toggle.show 
    {
      border: none;
    }
    .drop-log.show .dropdown-toggle:after
    {
      -webkit-transform: translate(-50%, -50%) rotate(-135deg);
      -ms-transform: translate(-50%, -50%) rotate(-135deg);
      transform: translate(-50%, -50%) rotate(-135deg);
      border-color: #131720;
    }
    .box-content
    {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      column-gap: 1rem;
    }
    .item-template:not(:last-child) {
      border-bottom:1px solid #ccc;
    }
    .item-template .image {
      max-width: 200px;
    }
    .item-template .image img {
      width: 100%;
    }
    .tabcontent-sub {
      min-height: 550px;
    }
    /* end subtab */
    @media screen and (max-width: 767px) {
      .sidenav {
        height: auto;
        padding: 15px;
      }
      .row.content {height: auto;} 
    }
  </style>
</head>
<body>

<div class="container-fluid px-0" style="max-height:80px;">
  <div class="bg-black" style="border-bottom: 1px solid #131720; padding:5px 15px;">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <img src="<?php echo plugin_dir_url(__FILE__); ?>img/logo_trans.png"/>
      </div>
      <div class="user">
        <img src="<?php echo plugin_dir_url(__FILE__); ?>img/user.png" alt="">
        <button type="button" class="btn text-white">
          <?php 
            $current_user = wp_get_current_user();
            echo esc_html($current_user->first_name . ' ' . $current_user->last_name);
            // If first and last name are empty, fall back to display name
            if (empty(trim($current_user->first_name . ' ' . $current_user->last_name))) {
              echo esc_html($current_user->display_name);
            }
          ?>
        </button>
        <div class="dropdown drop-log">
          <button type="button" class="btn dropdown-toggle" data-bs-toggle="dropdown"></button>
          <ul class="dropdown-menu mt-3">
            <li><a class="dropdown-item" href="<?php echo home_url('/designer/info'); ?>">Edit info</a></li>
            <li><a class="dropdown-item" href="<?php echo wp_logout_url(home_url('/designer/login')); ?>">Logout</a></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
