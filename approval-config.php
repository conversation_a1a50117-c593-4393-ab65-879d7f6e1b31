<?php
/**
 * Approval Configuration File
 * 
 * This file allows you to easily switch between different approval methods
 * to fix the HTTP 524 timeout error.
 */

// Prevent direct access
if (!defined('ABSPATH')) exit;

/**
 * APPROVAL METHOD CONFIGURATION
 * 
 * Choose your preferred method:
 * 
 * METHOD 1: 'quick_approval' 
 *   - ✅ Fixes 524 errors immediately
 *   - ✅ No API backup (faster)
 *   - ✅ Works with existing UI
 *   - ❌ No backup created
 * 
 * METHOD 2: 'async_processing'
 *   - ✅ Fixes 524 errors
 *   - ✅ Creates API backup
 *   - ✅ Shows progress to user
 *   - ⚠️ Requires JavaScript changes
 */

define('IPT_APPROVAL_METHOD', 'quick_approval'); // Change to 'async_processing' for full solution

/**
 * Get the appropriate AJAX action based on configuration
 */
function ipt_get_approval_action() {
    switch (IPT_APPROVAL_METHOD) {
        case 'quick_approval':
            return 'ipt_handle_quick_approval';
        case 'async_processing':
            return 'ipt_handle_api_backup_and_approval';
        default:
            return 'ipt_handle_quick_approval'; // Safe default
    }
}

/**
 * Get configuration info for display
 */
function ipt_get_approval_config_info() {
    $method = IPT_APPROVAL_METHOD;
    
    $configs = array(
        'quick_approval' => array(
            'name' => 'Quick Approval',
            'description' => 'Bypasses API backup for immediate approval (fixes 524 errors)',
            'pros' => array('Fast', 'No timeouts', 'Works immediately'),
            'cons' => array('No API backup created'),
            'status' => '✅ Active - No 524 errors'
        ),
        'async_processing' => array(
            'name' => 'Async Processing',
            'description' => 'Full backup and approval with progress tracking',
            'pros' => array('Creates backup', 'Progress tracking', 'Better UX'),
            'cons' => array('Requires JavaScript changes'),
            'status' => '🔄 Active - Background processing'
        )
    );
    
    return isset($configs[$method]) ? $configs[$method] : $configs['quick_approval'];
}

/**
 * Display configuration status in admin
 */
function ipt_approval_config_admin_notice() {
    if (current_user_can('manage_options')) {
        $config = ipt_get_approval_config_info();
        
        echo '<div class="notice notice-info">';
        echo '<p><strong>Approval Method:</strong> ' . $config['name'] . '</p>';
        echo '<p>' . $config['description'] . '</p>';
        echo '<p><strong>Status:</strong> ' . $config['status'] . '</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'ipt_approval_config_admin_notice');

/**
 * Add settings page for easy configuration
 */
function ipt_approval_config_menu() {
    add_options_page(
        'Approval Configuration',
        'Approval Config',
        'manage_options',
        'ipt-approval-config',
        'ipt_approval_config_page'
    );
}
add_action('admin_menu', 'ipt_approval_config_menu');

/**
 * Configuration page content
 */
function ipt_approval_config_page() {
    if (isset($_POST['update_config'])) {
        $new_method = sanitize_text_field($_POST['approval_method']);
        
        // Update the configuration file
        $config_content = file_get_contents(__FILE__);
        $config_content = preg_replace(
            "/define\('IPT_APPROVAL_METHOD', '[^']*'\);/",
            "define('IPT_APPROVAL_METHOD', '{$new_method}');",
            $config_content
        );
        file_put_contents(__FILE__, $config_content);
        
        echo '<div class="notice notice-success"><p>Configuration updated! Please refresh the page.</p></div>';
    }
    
    $current_config = ipt_get_approval_config_info();
    ?>
    <div class="wrap">
        <h1>🔧 Approval Configuration</h1>
        
        <div class="card" style="max-width: 800px;">
            <h2>Current Configuration</h2>
            <table class="form-table">
                <tr>
                    <th>Method:</th>
                    <td><strong><?php echo $current_config['name']; ?></strong></td>
                </tr>
                <tr>
                    <th>Description:</th>
                    <td><?php echo $current_config['description']; ?></td>
                </tr>
                <tr>
                    <th>Status:</th>
                    <td><?php echo $current_config['status']; ?></td>
                </tr>
                <tr>
                    <th>Pros:</th>
                    <td><?php echo implode(', ', $current_config['pros']); ?></td>
                </tr>
                <tr>
                    <th>Cons:</th>
                    <td><?php echo implode(', ', $current_config['cons']); ?></td>
                </tr>
            </table>
        </div>

        <div class="card" style="max-width: 800px; margin-top: 20px;">
            <h2>Change Configuration</h2>
            <form method="post">
                <table class="form-table">
                    <tr>
                        <th>Approval Method:</th>
                        <td>
                            <label>
                                <input type="radio" name="approval_method" value="quick_approval" 
                                       <?php checked(IPT_APPROVAL_METHOD, 'quick_approval'); ?>>
                                <strong>Quick Approval</strong> - Immediate fix for 524 errors (recommended to start)
                            </label><br><br>
                            
                            <label>
                                <input type="radio" name="approval_method" value="async_processing" 
                                       <?php checked(IPT_APPROVAL_METHOD, 'async_processing'); ?>>
                                <strong>Async Processing</strong> - Full solution with progress tracking
                            </label>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button('Update Configuration', 'primary', 'update_config'); ?>
            </form>
        </div>

        <div class="card" style="max-width: 800px; margin-top: 20px;">
            <h2>📋 Implementation Steps</h2>
            
            <h3>Step 1: Quick Fix (Current)</h3>
            <ol>
                <li>✅ Configuration file created</li>
                <li>✅ Quick approval method activated</li>
                <li>🔄 Test the approval process (should work without 524 errors)</li>
            </ol>
            
            <h3>Step 2: Full Solution (Optional)</h3>
            <ol>
                <li>Switch to "Async Processing" above</li>
                <li>Update your approval buttons to use <code>data-action="backup-approval"</code></li>
                <li>Ensure JavaScript handler is loaded</li>
                <li>Test with progress tracking</li>
            </ol>
            
            <p><strong>Need help?</strong> Check the test page: <a href="<?php echo home_url('/?test-approval'); ?>" target="_blank">Test Approval Functions</a></p>
        </div>
    </div>
    <?php
}
?>
