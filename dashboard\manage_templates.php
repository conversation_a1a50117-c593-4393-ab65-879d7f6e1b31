<?php include plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_header.php'; ?>
<style>
  /* Điều chỉnh khoảng cách giữa các nút trong thanh công cụ */
  .btn-edit {
    gap: 5px; /* <PERSON><PERSON>ảng cách giữa các nút */
    flex-wrap: wrap; /* Cho phép các nút xuống dòng nếu không đủ không gian */
  }

  .btn-edit .action-btn {
    padding: 5px 8px;
    border-radius: 4px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    white-space: nowrap;
    min-width: 80px;
    text-align: center;
    transition: all 0.2s ease;
  }

  .btn-edit .action-btn:hover:not([style*="cursor:not-allowed"]) {
    background-color: #e9ecef;
  }

  .btn-edit .action-btn i {
    margin-right: 5px;
  }

  .btn-edit .action-btn a {
    text-decoration: none;
    color: #333;
    font-size: 13px;
  }

  /* <PERSON><PERSON><PERSON><PERSON> chỉnh kích thước chữ và icon */
  @media (max-width: 1200px) {
    .btn-edit .action-btn {
      min-width: 70px;
      padding: 4px 6px;
    }
    
    .btn-edit .action-btn a {
      font-size: 12px;
    }
  }

  /* Điều chỉnh cho màn hình nhỏ hơn */
  @media (max-width: 992px) {
    .btn-edit {
      flex-wrap: wrap;
      gap: 8px;
      justify-content: flex-start !important;
    }
    
    .btn-edit .action-btn {
      margin-bottom: 5px;
    }
  }
</style>
<div class="container-fluid px-0">
  <div class="row mx-0">
    <?php include plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_sidebar.php'; ?>
    <div id="dash" class="tabcontent">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <p class="mb-0 fs-4 fw-semibold">Templates Management</p>
        <div class="btn-cre"><a href="<?php echo home_url('/designer/create-template'); ?>">Create template</a></div>
      </div>
      <div class="mb-4 box-content">
        <div class="item text-center">
          <p class="title">Total Templates</p>
          <p class="number mb-0" id="total-templates">
            <i class="fa fa-spinner fa-spin"></i>
          </p>
        </div>
        <div class="item text-center">
          <p class="title">Published Templates</p>
          <p class="number mb-0" id="published-templates">
            <i class="fa fa-spinner fa-spin"></i>
          </p>
        </div>
        <div class="item text-center">
          <p class="title">Used by customers</p>
          <p class="number mb-0" id="used-templates">
            <i class="fa fa-spinner fa-spin"></i>
          </p>
        </div>
        <div class="item text-center">
          <p class="title">Unpublish</p>
          <p class="number mb-0" id="unpublished-templates">
            <i class="fa fa-spinner fa-spin"></i>
          </p>
        </div>
      </div>
      <div class="content-form">
        <div class="d-flex justify-content-between align-items-center mb-2 px-3 pt-2">
          <p class="mb-0 title fw-semibold">Recent Templates</p>
          <div class="item-find">
            <!-- <div class="d-inline btn-sle">
              <i class="fa fa-filter"></i>
              <select id="filter-template">
                <option value="all">Filter</option>
                
              </select>
            </div> -->
            <div class="d-inline mt-4 btn-search mb-3">
              <i class="fa fa-search"></i><input type="text" placeholder="Search..">
            </div>            
          </div>
        </div>
        <div class="detail-content">
          <div class="tab-sub">
            <button class="tablinks-sub" onclick="openStatus(event, 'unpublish')" id="defaultOpensub">Unpublish</button>
            <button class="tablinks-sub" onclick="openStatus(event, 'pending')">Pending Review</button>
            <button class="tablinks-sub" onclick="openStatus(event, 'published')">Published (Approved)</button>
            <button class="tablinks-sub" onclick="openStatus(event, 'rejected')">Rejected</button>
          </div>
          <!-- unpublish -->
          <div id="unpublish" class="tabcontent-sub">
            <div id="templates-container">
              <!-- Templates will be loaded here via AJAX -->
              <div class="text-center py-4">
                <i class="fa fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Loading templates...</p>
              </div>
            </div>
          </div>
          <!-- pending -->
          <div id="pending" class="tabcontent-sub">
            <!-- <div class="content">
              <div class="image">
                <img src="<?php echo plugin_dir_url(__FILE__); ?>img/avatar.jpg">
              </div>
              <div class="details">
                <div class="mb-4">
                  <ul>
                    <li><strong>Template Name</strong>: Pending Review</li>
                    <li><strong>Last Update</strong>: 3 Apr, 2025</li>
                    <li><strong>Current Status</strong>: Draft</li>
                    <li><strong>Industry</strong>: Hotel</li>
                  </ul>
                </div>
                <div class="d-flex justify-content-between align-items-center btn-edit">
                  <div><i class="fa fa-pencil"></i><a href="#">Edit Template</a></div>
                  <div><i class="fa fa-tags"></i><a href="#">Tagging Template</a></div>
                  <div><i class="fa fa-eye"></i><a href="#">Preview</a></div>
                  <div><i class="fa fa-download"></i><a href="#">Download CSV</a></div>
                  <div><i class="fa fa-upload"></i><a href="#">Submit for review</a></div>
                </div>
              </div>
            </div> -->
          </div>
          <!-- published -->
          <div id="published" class="tabcontent-sub">
            <!-- <div class="content">
              <div class="image">
                <img src="<?php echo plugin_dir_url(__FILE__); ?>img/avatar.jpg">
              </div>
              <div class="details">
                <div class="mb-4">
                  <ul>
                    <li><strong>Template Name</strong>: Published</li>
                    <li><strong>Last Update</strong>: 3 Apr, 2025</li>
                    <li><strong>Current Status</strong>: Draft</li>
                    <li><strong>Industry</strong>: Hotel</li>
                  </ul>
                </div>
                <div class="d-flex justify-content-between align-items-center btn-edit">
                  <div><i class="fa fa-pencil"></i><a href="#">Edit Template</a></div>
                  <div><i class="fa fa-tags"></i><a href="#">Tagging Template</a></div>
                  <div><i class="fa fa-eye"></i><a href="#">Preview</a></div>
                  <div><i class="fa fa-download"></i><a href="#">Download CSV</a></div>
                  <div><i class="fa fa-upload"></i><a href="#">Submit for review</a></div>
                </div>
              </div>
            </div> -->
          </div>
          <!-- rejected -->
          <div id="rejected" class="tabcontent-sub">
            <!-- <div class="content">
              <div class="image">
                <img src="<?php echo plugin_dir_url(__FILE__); ?>img/avatar.jpg">
              </div>
              <div class="details">
                <div class="mb-4">
                  <ul>
                    <li><strong>Template Name</strong>: Rejected</li>
                    <li><strong>Last Update</strong>: 3 Apr, 2025</li>
                    <li><strong>Current Status</strong>: Draft</li>
                    <li><strong>Industry</strong>: Hotel</li>
                  </ul>
                </div>
                <div class="d-flex justify-content-between align-items-center btn-edit">
                  <div><i class="fa fa-pencil"></i><a href="#">Edit Template</a></div>
                  <div><i class="fa fa-tags"></i><a href="#">Tagging Template</a></div>
                  <div><i class="fa fa-eye"></i><a href="#">Preview</a></div>
                  <div><i class="fa fa-download"></i><a href="#">Download CSV</a></div>
                  <div><i class="fa fa-upload"></i><a href="#">Submit for review</a></div>
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>

    <!-- <div id="temp" class="tabcontent">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <p class="mb-0">Templates Management</p>
        <div class="btn-cre"><a href="#" target="_self">Create template</a></div>
      </div>
      <div class="mb-4 box-content">
        <div class="item text-center">
          <p class="title">Total Templates</p>
          <p class="number mb-0">200</p>
        </div>
        <div class="item text-center">
          <p class="title">Published Templates</p>
          <p class="number mb-0">100</p>
        </div>
        <div class="item text-center">
          <p class="title">Used by customers</p>
          <p class="number mb-0">50</p>
        </div>
        <div class="item text-center">
          <p class="title">Unpublish</p>
          <p class="number mb-0">2</p>
        </div>
      </div>
      <div class="content-form">
        <div class="d-flex justify-content-between align-items-center mb-2 px-3 pt-2">
          <p class="mb-0 title">Recent Templates</p>
          <div class="item-find">
            <div class="d-inline btn-sle">
              <i class="fa fa-filter"></i>
              <select id="filter-template">
                <option value="all">Filter</option>
                <option value="Africa">Africa</option>
                <option value="Americas">Americas</option>
                <option value="Asia">Asia</option>
                <option value="Europe">Europe</option>
                <option value="Oceania">Oceania</option>
              </select>
            </div>
            <div class="d-inline mt-4 btn-search mb-3">
              <i class="fa fa-search"></i><input type="text" placeholder="Search..">
            </div>
          </div>
        </div>
        <div class="detail-content">
          <div class="tab-sub">
            <button class="tablinks-sub" onclick="openStatus2(event, 'unpublish2')">Unpublish</button>
            <button class="tablinks-sub" onclick="openStatus2(event, 'pending2')">Pending Review</button>
            <button class="tablinks-sub" onclick="openStatus2(event, 'published2')">Published (Approved)</button>
            <button class="tablinks-sub" onclick="openStatus2(event, 'rejected2')">Rejected</button>
          </div>

          <div id="unpublish2" class="tabcontent-sub">
            <div class="content">
              <div class="image">
                <img src="<?php echo plugin_dir_url(__FILE__); ?>img/avatar.jpg">
              </div>
              <div class="details">
                <div class="mb-4">
                  <ul>
                    <li><strong>Template Name</strong>: Website for testing 1</li>
                    <li><strong>Last Update</strong>: 3 Apr, 2025</li>
                    <li><strong>Current Status</strong>: Draft</li>
                    <li><strong>Industry</strong>: Hotel</li>
                  </ul>
                </div>
                <div class="d-flex justify-content-between align-items-center btn-edit">
                  <div><i class="fa fa-pencil"></i><a href="#">Edit Template</a></div>
                  <div><i class="fa fa-tags"></i><a href="#">Tagging Template</a></div>
                  <div><i class="fa fa-eye"></i><a href="#">Preview</a></div>
                  <div><i class="fa fa-download"></i><a href="#">Download CSV</a></div>
                  <div><i class="fa fa-upload"></i><a href="#">Submit for review</a></div>
                </div>
              </div>
            </div>
          </div>

          <div id="pending2" class="tabcontent-sub">
            <div class="content">
              <div class="image">
                <img src="<?php echo plugin_dir_url(__FILE__); ?>img/avatar.jpg">
              </div>
              <div class="details">
                <div class="mb-4">
                  <ul>
                    <li><strong>Template Name</strong>: Pending Review</li>
                    <li><strong>Last Update</strong>: 3 Apr, 2025</li>
                    <li><strong>Current Status</strong>: Draft</li>
                    <li><strong>Industry</strong>: Hotel</li>
                  </ul>
                </div>
                <div class="d-flex justify-content-between align-items-center btn-edit">
                  <div><i class="fa fa-pencil"></i><a href="#">Edit Template</a></div>
                  <div><i class="fa fa-tags"></i><a href="#">Tagging Template</a></div>
                  <div><i class="fa fa-eye"></i><a href="#">Preview</a></div>
                  <div><i class="fa fa-download"></i><a href="#">Download CSV</a></div>
                  <div><i class="fa fa-upload"></i><a href="#">Submit for review</a></div>
                </div>
              </div>
            </div>
          </div>

          <div id="published2" class="tabcontent-sub">
            <div class="content">
              <div class="image">
                <img src="<?php echo plugin_dir_url(__FILE__); ?>img/avatar.jpg">
              </div>
              <div class="details">
                <div class="mb-4">
                  <ul>
                    <li><strong>Template Name</strong>: Published</li>
                    <li><strong>Last Update</strong>: 3 Apr, 2025</li>
                    <li><strong>Current Status</strong>: Draft</li>
                    <li><strong>Industry</strong>: Hotel</li>
                  </ul>
                </div>
                <div class="d-flex justify-content-between align-items-center btn-edit">
                  <div><i class="fa fa-pencil"></i><a href="#">Edit Template</a></div>
                  <div><i class="fa fa-tags"></i><a href="#">Tagging Template</a></div>
                  <div><i class="fa fa-eye"></i><a href="#">Preview</a></div>
                  <div><i class="fa fa-download"></i><a href="#">Download CSV</a></div>
                  <div><i class="fa fa-upload"></i><a href="#">Submit for review</a></div>
                </div>
              </div>
            </div>
          </div>

          <div id="rejected2" class="tabcontent-sub">
            <div class="content">
              <div class="image">
                <img src="<?php echo plugin_dir_url(__FILE__); ?>img/avatar.jpg">
              </div>
              <div class="details">
                <div class="mb-4">
                  <ul>
                    <li><strong>Template Name</strong>: Rejected</li>
                    <li><strong>Last Update</strong>: 3 Apr, 2025</li>
                    <li><strong>Current Status</strong>: Draft</li>
                    <li><strong>Industry</strong>: Hotel</li>
                  </ul>
                </div>
                <div class="d-flex justify-content-between align-items-center btn-edit">
                  <div><i class="fa fa-pencil"></i><a href="#">Edit Template</a></div>
                  <div><i class="fa fa-tags"></i><a href="#">Tagging Template</a></div>
                  <div><i class="fa fa-eye"></i><a href="#">Preview</a></div>
                  <div><i class="fa fa-download"></i><a href="#">Download CSV</a></div>
                  <div><i class="fa fa-upload"></i><a href="#">Submit for review</a></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
  </div>
</div>
<script>
  function openForm(evt, formName) {
    var i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
    }
    tablinks = document.getElementsByClassName("tablinks");
    for (i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active", "");
    }
    document.getElementById(formName).style.display = "block";
    evt.currentTarget.className += " active";
  }

  // Get the element with id="defaultOpen" and click on it
  // document.getElementById("defaultOpen").click();
</script>
<script>
  function openStatus(evt, statusName) {
    var i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("tabcontent-sub");
    for (i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
    }
    tablinks = document.getElementsByClassName("tablinks-sub");
    for (i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active-sub", "");
    }
    document.getElementById(statusName).style.display = "block";
    evt.currentTarget.className += " active-sub";
  }
  // Get the element with id="defaultOpensub" and click on it
  document.getElementById("defaultOpensub").click();
</script>
<script>
  function openStatus2(evt, statusName2) {
    var i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("tabcontent-sub");
    for (i = 0; i < tabcontent.length; i++) {
      tabcontent[i].style.display = "none";
    }
    tablinks = document.getElementsByClassName("tablinks-sub");
    for (i = 0; i < tablinks.length; i++) {
      tablinks[i].className = tablinks[i].className.replace(" active-sub", "");
    }
    document.getElementById(statusName2).style.display = "block";
    evt.currentTarget.className += " active-sub";
  }
</script>

<script>
$(document).ready(function() {
  // Get designer_id from user meta
  <?php
    $current_user_id = get_current_user_id();
    $designer_id = get_user_meta($current_user_id, 'designer_id', true);
    // Fallback to 1 if no designer_id found
    if (empty($designer_id)) {
        $designer_id = 1;
    } else {
        // Convert to integer for consistency
        $designer_id = intval($designer_id);
    }
  ?>
  const designerId = <?php echo json_encode($designer_id); ?>;

  // Fetch templates on page load (this will also update statistics)
  fetchTemplates();

  // Function to fetch templates from API
  function fetchTemplates(keyword = "", industry_selected = "") {
    // Use filters for manage templates with dynamic designer_id from user meta
    const filters = ["is_kit:=(0)", "designer_id:=(" + designerId + ")"];
    const search = keyword ? keyword : "";
    const sort_by = "created_at:DESC";

    const query = `
      query Template_list($filters: [String!], $search: String!, $sort: String!) {
        template_list(body: {filters: $filters, search: $search, sort: $sort, }) {
              totalCount
              totalPages
              currentPage
              data {
                  id
                  name
                  desc
                  image_id
                  image {
                    id
                    file_name
                    file_url
                  }
                  industries {
                    id
                    name
                  }
                  info
                  status_id
              }
        }
      }`;

    $.ajax({
      url: iptHomeAjax.ajax_url,
      type: 'POST',
      dataType: 'json',
      data: {
            action: 'ipt_home_graphql',
            query: query,
            variables: JSON.stringify({ filters: filters, search: search, sort: sort_by })
      },
      success: function(response) {
            console.log('=== MANAGE TEMPLATES API RESPONSE ===');
            console.log(response);

            if(response.data && response.data.template_list) {
                const templates = response.data.template_list.data;

                // Render templates
                renderTemplates(templates, industry_selected);

                // Calculate and update statistics from template data
                calculateStatisticsFromTemplates(templates);
            } else {
                $('#templates-container').html('<div class="text-center py-4"><p>No templates found.</p></div>');

                // No templates found, set all counts to 0
                updateStatisticsDisplay(0, 0);
            }
      },
      error: function(xhr, status, error) {
            console.error('Error fetching templates:', error);
            $('#templates-container').html('<div class="text-center py-4 text-danger"><p>Error loading templates. Please try again.</p></div>');
      }
    });
  }

  // Function to render templates into appropriate tab containers
  function renderTemplates(list, industry_selected = "") {
    // Initialize HTML for each tab
    let unpublishHtml = '';
    let pendingHtml = '';
    let publishedHtml = '';
    let rejectedHtml = '';

    // Counters for each status
    let unpublishCount = 0;
    let pendingCount = 0;
    let publishedCount = 0;
    let rejectedCount = 0;

    if (list.length === 0) {
      const noDataHtml = '<div class="text-center py-4"><p>No templates found.</p></div>';
      unpublishHtml = noDataHtml;
      pendingHtml = noDataHtml;
      publishedHtml = noDataHtml;
      rejectedHtml = noDataHtml;
    } else {
      list.forEach(function(item) {
        // Get industry names from API response (support multiple industries)
        let industry_name = "No Industry";

        console.log(`Template ${item.id} raw industries data:`, item.industries);

        if (item.industries && Array.isArray(item.industries) && item.industries.length > 0) {
          console.log(`Template ${item.id} industries array:`, item.industries);

          // Get all valid industry names
          const validIndustries = item.industries
            .filter(industry => industry && industry.name && industry.name.trim() !== '')
            .map(industry => industry.name.trim());

          if (validIndustries.length > 0) {
            industry_name = validIndustries.join(', ');
            console.log(`✅ Template ${item.id} industries found (${validIndustries.length}):`, industry_name);
          } else {
            console.log(`❌ Template ${item.id} has industries but all names are empty or invalid:`, item.industries);
          }
        } else {
          console.log(`❌ Template ${item.id} has no industries array or it's empty`);
        }

        // Get status name from status_id
        let status_name = "";
        if (item.status_id === 1) {
          status_name = "Pending";
        } else if (item.status_id === 2) {
          status_name = "Published";
        } else if (item.status_id === 3) {
          status_name = "Rejected";
        } else {
          status_name = "";
        }

        // Filter by industry if specified
        // if(industry_selected.length > 0) {
        //   if(industry_selected !== industry_name) {
        //     return;
        //   }
        // }
        
        // Generate URLs using domain from item.info if available
        let domain = `template-${item.id}.weaveform.com`; // Default domain
        let page_id_edit = 2;
        if (item.info && item.info.domain) {
          domain = item.info.domain;
        }
        if (item.info && item.info.template_id) {
          page_id_edit = 1;
        }

        let urlTagging = `https://${domain}/?user_id=${designerId}&template_id=${item.id}&bypass_token=<?php echo BYPASS_LOGIN_TOKEN; ?>`;
        let urlEdit = `https://${domain}/wp-admin/post.php?post=${page_id_edit}&action=elementor&bypass_token=<?php echo BYPASS_LOGIN_TOKEN; ?>`;
        let urlPreview = `https://${domain}/?template_preview=true`;

        

        console.log(`Template ${item.id} URLs generated with user_id=${designerId}:`, {
          tagging: urlTagging,
          edit: urlEdit
        });

        // Generate template HTML
        const templateHtml = `
          <div class="content item-template" style="">
            <div class="image">
              <img src="${item.image ? item.image.file_url : 'https://placehold.co/600x400/EEE/31343C?text=No+Image'}" alt="${item.name}">
            </div>
            <div class="details">
              <div class="mb-4">
                <ul>
                  <li><strong>Template Name</strong>: ${item.name}</li>
                  <li><strong>Last Update</strong>: ${new Date().toLocaleDateString()}</li>
                  <li><strong>Current Status</strong>: ${status_name}</li>
                  <li><strong>Industry</strong>: ${industry_name}</li>
                </ul>
              </div>
              <div class="d-flex gap-4 align-items-center btn-edit">
                <div class="action-btn" style="cursor:pointer;">
                  <i class="fa fa-pencil"></i>
                  <a target="_blank" href="${urlEdit}">Edit Template</a>
                </div>
                <div class="action-btn" style="cursor:pointer;">
                  <i class="fa fa-tags"></i>
                  <a target="_blank" href="${urlTagging}">Tag Template</a>
                </div>
                <div class="action-btn"  style="cursor:pointer;">
                  <i class="fa fa-eye"></i>
                  <a target="_blank" href="${urlPreview}">Preview</a>
                </div>
                <div class="action-btn" style="background:#ccc !important;cursor:not-allowed;">
                  <i class="fa fa-download"></i>
                  <a href="#" style="pointer-events: none; opacity: 0.6;">Download CSV</a>
                </div>
                <div class="action-btn" style="background:#ccc !important;cursor:not-allowed;">
                  <i class="fa fa-upload"></i>
                  <a href="#" style="pointer-events: none; opacity: 0.6;">Submit for review</a>
                </div>
              </div>
            </div>
          </div>
        `;

        // Distribute templates to appropriate tab based on status_id
        if (item.status_id === 1) {
          // status_id = 1 goes to both "unpublish" and "pending" tabs
          unpublishHtml += templateHtml;
          pendingHtml += templateHtml;
          unpublishCount++;
          pendingCount++;
        } else if (item.status_id === 2) {
          // status_id = 2 goes to "published" tab
          publishedHtml += templateHtml;
          publishedCount++;
        } else if (item.status_id === 3) {
          // status_id = 3 goes to "rejected" tab
          rejectedHtml += templateHtml;
          rejectedCount++;
        }

        console.log(`Template ${item.id} (status_id: ${item.status_id}) added to appropriate tab(s)`);
      });
    }

    // Set default message for empty tabs
    if (unpublishHtml === '') unpublishHtml = '<div class="text-center py-4"><p>No unpublished templates found.</p></div>';
    if (pendingHtml === '') pendingHtml = '<div class="text-center py-4"><p>No pending templates found.</p></div>';
    if (publishedHtml === '') publishedHtml = '<div class="text-center py-4"><p>No published templates found.</p></div>';
    if (rejectedHtml === '') rejectedHtml = '<div class="text-center py-4"><p>No rejected templates found.</p></div>';

    // Render templates into appropriate tab containers
    $('#unpublish').html(unpublishHtml);
    $('#pending').html(pendingHtml);
    $('#published').html(publishedHtml);
    $('#rejected').html(rejectedHtml);

    console.log('=== TEMPLATES DISTRIBUTED TO TABS ===');
    console.log(`Unpublish tab: ${unpublishCount} templates`);
    console.log(`Pending tab: ${pendingCount} templates`);
    console.log(`Published tab: ${publishedCount} templates`);
    console.log(`Rejected tab: ${rejectedCount} templates`);
  }

  // Search functionality
  $('.btn-search input').on('keyup', function() {
    const keyword = $(this).val();
    fetchTemplates(keyword);
  });

  // Filter functionality
  $('#filter-template').on('change', function() {
    const filterValue = $(this).val();
    if (filterValue === 'all') {
      fetchTemplates();
    } else {
      fetchTemplates("", filterValue);
    }
  });
  // Function to calculate statistics from existing template data
  function calculateStatisticsFromTemplates(templates) {
    console.log('=== CALCULATING STATISTICS FROM TEMPLATE DATA ===');
    console.log('Templates received:', templates);

    let pending = 0;    // status_id = 1
    let published = 0;  // status_id = 2

    templates.forEach(template => {
      if (template.status_id === 1) {
        pending++;
      } else if (template.status_id === 2) {
        published++;
      }
    });

    const total = templates.length;

    console.log('Statistics calculated:');
    console.log('- Total:', total);
    console.log('- Pending (status_id=1):', pending);
    console.log('- Published (status_id=2):', published);

    // Update the display
    updateStatisticsDisplay(total, published);
  }

  // Function to update statistics display (simplified for 2 statuses)
  function updateStatisticsDisplay(total, published) {
    const pending = total - published;

    console.log('=== UPDATING STATISTICS DISPLAY ===');
    console.log('Total:', total);
    console.log('Published:', published);
    console.log('Pending:', pending);

    $('#total-templates').text(total);
    $('#published-templates').text(published);
    $('#used-templates').text(0); // Not used for now
    $('#unpublished-templates').text(pending);
  }

});
</script>

<?php include  plugin_dir_path(dirname(__FILE__)) . 'dashboard/designer_footer.php'; ?>
