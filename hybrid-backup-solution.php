<?php
/**
 * Hybrid Backup Solution
 * 
 * Flow:
 * 1. Call API to create backup (non-blocking)
 * 2. Poll local directory every 10 seconds for .wpress file
 * 3. Upload backup file to API when ready
 * 4. Return code_file_id for approval process
 */

// Prevent direct access
if (!defined('ABSPATH')) exit;

/**
 * AJAX handler for hybrid backup and approval
 */
function ipt_handle_hybrid_backup_approval() {
    // Check nonce for security
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_approval_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    error_log("=== HYBRID BACKUP APPROVAL PROCESS STARTED ===");

    // Get approval parameters
    $approval_action = sanitize_text_field($_POST['approval_action']);
    $template_id = intval($_POST['template_id']);
    $page_url = sanitize_url($_POST['page_url']);
    $page_params = sanitize_text_field($_POST['page_params']);

    if (empty($approval_action) || !in_array($approval_action, ['approve', 'reject'])) {
        wp_send_json_error(array('message' => 'Invalid approval action.'));
        return;
    }

    // Generate unique process ID
    $process_id = wp_generate_uuid4();
    
    // Store process data
    $process_data = array(
        'status' => 'starting',
        'step' => 'api_backup_request',
        'approval_action' => $approval_action,
        'template_id' => $template_id,
        'page_url' => $page_url,
        'page_params' => $page_params,
        'started_at' => current_time('mysql'),
        'last_check' => null,
        'backup_file' => null,
        'code_file_id' => null,
        'error' => null
    );

    // Store in transient (expires in 1 hour)
    set_transient('hybrid_backup_' . $process_id, $process_data, 3600);

    // Step 1: Call API to create backup (non-blocking)
    $api_result = ipt_trigger_api_backup_creation();
    
    if (!$api_result['success']) {
        error_log("❌ API backup trigger failed: " . $api_result['message']);
        wp_send_json_error(array(
            'message' => 'Failed to trigger backup creation',
            'error' => $api_result['message']
        ));
        return;
    }

    error_log("✅ API backup creation triggered successfully");

    // Update process status
    $process_data['status'] = 'waiting_for_backup';
    $process_data['step'] = 'polling_local_files';
    $process_data['api_triggered_at'] = current_time('mysql');
    set_transient('hybrid_backup_' . $process_id, $process_data, 3600);

    // Schedule the polling process to start immediately
    wp_schedule_single_event(time(), 'ipt_start_backup_polling', array($process_id));

    // Return immediately to user
    wp_send_json_success(array(
        'message' => 'Backup process initiated successfully',
        'process_id' => $process_id,
        'status' => 'waiting_for_backup',
        'polling_started' => true,
        'estimated_time' => '5-15 minutes'
    ));
}
add_action('wp_ajax_ipt_handle_hybrid_backup_approval', 'ipt_handle_hybrid_backup_approval');
add_action('wp_ajax_nopriv_ipt_handle_hybrid_backup_approval', 'ipt_handle_hybrid_backup_approval');

/**
 * Trigger API backup creation (non-blocking)
 */
function ipt_trigger_api_backup_creation() {
    // Step 1: Login to API
    $login_result = ipt_api_login();
    if (!$login_result['success']) {
        return array(
            'success' => false,
            'message' => 'API login failed: ' . $login_result['message']
        );
    }

    error_log("✅ API login successful, triggering backup creation...");

    // Step 2: Trigger backup creation (non-blocking)
    $backup_result = ipt_api_create_backup_non_blocking($login_result['token']);
    
    return $backup_result;
}

/**
 * Create backup via API (non-blocking version)
 */
function ipt_api_create_backup_non_blocking($token) {
    $backup_url = 'https://ipt-wp-api.weaveform.com/wpcli';
    $current_domain = $_SERVER['HTTP_HOST'];

    $backup_data = array(
        'domain_name' => $current_domain,
        'command' => 'ai1wm backup',
        'async' => true // Request async processing
    );

    $args = array(
        'method' => 'POST',
        'headers' => array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $token
        ),
        'body' => json_encode($backup_data),
        'timeout' => 30, // Short timeout since we're not waiting for completion
        'sslverify' => false,
        'user-agent' => 'WordPress/Designer-Tagging-Plugin-Hybrid'
    );

    error_log("🚀 Triggering non-blocking API backup for domain: {$current_domain}");

    $response = wp_remote_post($backup_url, $args);

    if (is_wp_error($response)) {
        error_log("❌ API backup trigger error: " . $response->get_error_message());
        return array(
            'success' => false,
            'message' => $response->get_error_message()
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);

    error_log("API backup trigger response: Code={$response_code}, Body={$response_body}");

    // Accept both 200 (immediate) and 202 (accepted for processing)
    if (in_array($response_code, [200, 202])) {
        return array(
            'success' => true,
            'message' => 'Backup creation triggered successfully',
            'response_code' => $response_code,
            'async' => ($response_code === 202)
        );
    } else {
        return array(
            'success' => false,
            'message' => "HTTP {$response_code}: {$response_body}"
        );
    }
}

/**
 * Start the backup file polling process
 */
function ipt_start_backup_polling($process_id) {
    error_log("🔍 Starting backup polling for process: {$process_id}");
    
    // Schedule the first poll immediately
    wp_schedule_single_event(time(), 'ipt_poll_backup_file', array($process_id, 0));
}
add_action('ipt_start_backup_polling', 'ipt_start_backup_polling');

/**
 * Poll for backup file creation (every 10 seconds)
 */
function ipt_poll_backup_file($process_id, $attempt_count) {
    error_log("🔍 Polling attempt #{$attempt_count} for process: {$process_id}");

    // Get process data
    $process_data = get_transient('hybrid_backup_' . $process_id);
    if ($process_data === false) {
        error_log("❌ Process data not found for: {$process_id}");
        return;
    }

    // Update last check time
    $process_data['last_check'] = current_time('mysql');
    $process_data['attempt_count'] = $attempt_count;

    // Check for backup file
    $backup_file = get_latest_backup_file_since($process_data['started_at']);
    
    if ($backup_file !== false) {
        error_log("✅ Backup file found: " . $backup_file['filename']);
        
        // Update process status
        $process_data['status'] = 'backup_found';
        $process_data['step'] = 'uploading_to_api';
        $process_data['backup_file'] = $backup_file;
        set_transient('hybrid_backup_' . $process_id, $process_data, 3600);

        // Schedule upload
        wp_schedule_single_event(time(), 'ipt_upload_backup_file', array($process_id));
        return;
    }

    // Check timeout (max 30 attempts = 5 minutes of polling)
    if ($attempt_count >= 30) {
        error_log("❌ Backup polling timeout after {$attempt_count} attempts");
        
        $process_data['status'] = 'timeout';
        $process_data['step'] = 'polling_timeout';
        $process_data['error'] = 'Backup file not created within timeout period';
        set_transient('hybrid_backup_' . $process_id, $process_data, 3600);
        return;
    }

    // Update process and schedule next poll in 10 seconds
    $process_data['status'] = 'polling';
    set_transient('hybrid_backup_' . $process_id, $process_data, 3600);
    
    wp_schedule_single_event(time() + 10, 'ipt_poll_backup_file', array($process_id, $attempt_count + 1));
    
    error_log("⏳ Scheduled next poll in 10 seconds (attempt #{$attempt_count})");
}
add_action('ipt_poll_backup_file', 'ipt_poll_backup_file', 10, 2);

/**
 * Get latest backup file created since a specific time
 */
function get_latest_backup_file_since($since_time) {
    $backup_dir = WP_CONTENT_DIR . '/ai1wm-backups';
    
    if (!is_dir($backup_dir)) {
        return false;
    }

    $since_timestamp = strtotime($since_time);
    $files = scandir($backup_dir);
    
    if ($files === false) {
        return false;
    }

    $recent_backups = array();
    foreach ($files as $file) {
        if (pathinfo($file, PATHINFO_EXTENSION) === 'wpress') {
            $file_path = $backup_dir . '/' . $file;
            if (is_file($file_path) && is_readable($file_path)) {
                $file_time = filemtime($file_path);
                
                // Only consider files created after the process started
                if ($file_time >= $since_timestamp) {
                    $recent_backups[] = array(
                        'filename' => $file,
                        'filepath' => $file_path,
                        'modified_time' => $file_time,
                        'size' => filesize($file_path),
                        'created_after_process' => true
                    );
                }
            }
        }
    }

    if (empty($recent_backups)) {
        return false;
    }

    // Sort by modification time (newest first)
    usort($recent_backups, function($a, $b) {
        return $b['modified_time'] - $a['modified_time'];
    });

    return $recent_backups[0];
}

/**
 * Upload backup file to API
 */
function ipt_upload_backup_file($process_id) {
    error_log("📤 Starting backup upload for process: {$process_id}");

    // Get process data
    $process_data = get_transient('hybrid_backup_' . $process_id);
    if ($process_data === false || !isset($process_data['backup_file'])) {
        error_log("❌ No backup file data found for process: {$process_id}");
        return;
    }

    $backup_file = $process_data['backup_file'];
    
    // Upload to API
    $upload_result = upload_backup_to_api($backup_file['filepath']);
    
    if ($upload_result['success']) {
        error_log("✅ Backup upload successful, code_file_id: " . $upload_result['code_file_id']);
        
        // Update process status
        $process_data['status'] = 'upload_complete';
        $process_data['step'] = 'completing_approval';
        $process_data['code_file_id'] = $upload_result['code_file_id'];
        $process_data['upload_completed_at'] = current_time('mysql');
        set_transient('hybrid_backup_' . $process_id, $process_data, 3600);

        // Schedule approval completion
        wp_schedule_single_event(time(), 'ipt_complete_hybrid_approval', array($process_id));
        
    } else {
        error_log("❌ Backup upload failed: " . $upload_result['message']);
        
        $process_data['status'] = 'upload_failed';
        $process_data['step'] = 'upload_error';
        $process_data['error'] = $upload_result['message'];
        set_transient('hybrid_backup_' . $process_id, $process_data, 3600);
    }
}
add_action('ipt_upload_backup_file', 'ipt_upload_backup_file');

/**
 * Complete the approval process
 */
function ipt_complete_hybrid_approval($process_id) {
    error_log("🎯 Completing approval for process: {$process_id}");

    // Get process data
    $process_data = get_transient('hybrid_backup_' . $process_id);
    if ($process_data === false) {
        error_log("❌ Process data not found for completion: {$process_id}");
        return;
    }

    // Extract approval data
    $approval_action = $process_data['approval_action'];
    $template_id = $process_data['template_id'];
    $page_url = $process_data['page_url'];
    $code_file_id = $process_data['code_file_id'];

    error_log("🔄 Starting template update with code_file_id: {$code_file_id}");

    // Step 1: Call GraphQL API to update template with code_file_id
    if ($approval_action === 'approve' && $code_file_id) {
        $graphql_result = ipt_update_template_code_file_id($template_id, $code_file_id);

        if (!$graphql_result['success']) {
            error_log("❌ Failed to update template code_file_id via GraphQL: " . $graphql_result['message']);

            // Update process with error
            $process_data['status'] = 'graphql_failed';
            $process_data['step'] = 'graphql_error';
            $process_data['error'] = 'Failed to update template: ' . $graphql_result['message'];
            set_transient('hybrid_backup_' . $process_id, $process_data, 3600);
            return;
        }

        error_log("✅ Template code_file_id updated successfully via GraphQL");
    }

    // Step 2: Update page meta with approval status
    $page_id = url_to_postid($page_url);
    if ($page_id) {
        $status_value = ($approval_action === 'approve') ? 1 : 0;

        update_post_meta($page_id, '_approval_status', $status_value);
        update_post_meta($page_id, '_approval_date', current_time('mysql'));
        update_post_meta($page_id, '_approval_user', get_current_user_id());
        update_post_meta($page_id, '_code_file_id', $code_file_id);

        error_log("✅ WordPress meta updated: page_id={$page_id}, status={$status_value}, code_file_id={$code_file_id}");
    }

    // Update final process status
    $process_data['status'] = 'completed';
    $process_data['step'] = 'finished';
    $process_data['completed_at'] = current_time('mysql');
    set_transient('hybrid_backup_' . $process_id, $process_data, 3600);

    error_log("🎉 Hybrid backup approval process completed successfully!");
}
add_action('ipt_complete_hybrid_approval', 'ipt_complete_hybrid_approval');

/**
 * AJAX handler to check hybrid backup process status
 */
function ipt_check_hybrid_backup_status() {
    if (!isset($_POST['process_id'])) {
        wp_send_json_error(array('message' => 'Process ID required'));
        return;
    }

    $process_id = sanitize_text_field($_POST['process_id']);
    $process_data = get_transient('hybrid_backup_' . $process_id);

    if ($process_data === false) {
        wp_send_json_error(array('message' => 'Process not found or expired'));
        return;
    }

    // Calculate progress percentage
    $progress = 0;
    switch ($process_data['status']) {
        case 'starting':
        case 'waiting_for_backup':
            $progress = 10;
            break;
        case 'polling':
            $progress = 20 + (isset($process_data['attempt_count']) ? min($process_data['attempt_count'] * 2, 50) : 0);
            break;
        case 'backup_found':
            $progress = 75;
            break;
        case 'upload_complete':
            $progress = 90;
            break;
        case 'completed':
            $progress = 100;
            break;
        case 'timeout':
        case 'upload_failed':
            $progress = 0;
            break;
    }

    wp_send_json_success(array(
        'process_id' => $process_id,
        'status' => $process_data['status'],
        'step' => $process_data['step'],
        'progress' => $progress,
        'last_check' => $process_data['last_check'],
        'error' => isset($process_data['error']) ? $process_data['error'] : null,
        'code_file_id' => isset($process_data['code_file_id']) ? $process_data['code_file_id'] : null,
        'backup_file' => isset($process_data['backup_file']) ? $process_data['backup_file']['filename'] : null,
        'completed' => in_array($process_data['status'], ['completed', 'timeout', 'upload_failed'])
    ));
}
add_action('wp_ajax_ipt_check_hybrid_backup_status', 'ipt_check_hybrid_backup_status');
add_action('wp_ajax_nopriv_ipt_check_hybrid_backup_status', 'ipt_check_hybrid_backup_status');

/**
 * Update template code_file_id via GraphQL API
 * This is called after backup upload to update the template with the new backup file ID
 */
function ipt_update_template_code_file_id($template_id, $code_file_id) {
    error_log("🔄 Updating template {$template_id} with code_file_id: {$code_file_id}");

    // Prepare GraphQL mutation to match your API format
    $mutation = '
        mutation Webhooks_templates_change_status($id: Int!, $status_id: Int!, $code_file_id: Int!) {
            webhooks_templates_change_status(
                id: $id
                body: {
                    status_id: $status_id
                    code_file_id: $code_file_id
                }
            )
        }
    ';

    $variables = array(
        'id' => intval($template_id),
        'status_id' => 2, // Approved status
        'code_file_id' => intval($code_file_id)
    );

    // Call GraphQL API
    $graphql_url = GRAPHQL_API_URL;
    $graphql_token = GRAPHQL_TOKEN;

    $post_data = array(
        'query' => $mutation,
        'variables' => $variables
    );

    $args = array(
        'method' => 'POST',
        'headers' => array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $graphql_token
        ),
        'body' => json_encode($post_data),
        'timeout' => 30,
        'sslverify' => false
    );

    error_log("🚀 Calling GraphQL API to update template code_file_id");
    error_log("URL: {$graphql_url}");
    error_log("Variables: " . json_encode($variables));

    $response = wp_remote_post($graphql_url, $args);

    if (is_wp_error($response)) {
        error_log("❌ GraphQL API error: " . $response->get_error_message());
        return array(
            'success' => false,
            'message' => $response->get_error_message()
        );
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $response_body = wp_remote_retrieve_body($response);

    error_log("GraphQL API response: Code={$response_code}, Body={$response_body}");

    if ($response_code !== 200) {
        return array(
            'success' => false,
            'message' => "HTTP {$response_code}: {$response_body}"
        );
    }

    $response_data = json_decode($response_body, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return array(
            'success' => false,
            'message' => 'Invalid JSON response from GraphQL API'
        );
    }

    // Check for GraphQL errors
    if (isset($response_data['errors']) && !empty($response_data['errors'])) {
        $error_message = $response_data['errors'][0]['message'] ?? 'Unknown GraphQL error';
        error_log("❌ GraphQL error: " . $error_message);
        return array(
            'success' => false,
            'message' => $error_message
        );
    }

    // Check for successful response
    if (isset($response_data['data'])) {
        error_log("✅ Template code_file_id updated successfully via GraphQL");
        return array(
            'success' => true,
            'data' => $response_data['data']
        );
    }

    return array(
        'success' => false,
        'message' => 'Unexpected GraphQL response format'
    );
}
?>
