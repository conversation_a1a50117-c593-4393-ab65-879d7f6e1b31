body {
   padding-top: 60px;
}
#plugin-topbar {
   width: 100%;
   position: fixed;
   top: 0;
   left: 0;
   z-index: 99999;
   height: 60px;
}
.cursor-pointer {
  cursor: pointer;
}
#main-sidebar{
   width: 63px; 
   top: 60px;
   left: 0px;
   position: fixed !important;
   height: 100%;
   background: #ccc;
   border-right: 1px solid #ccc;
   z-index: 9999 !important;
}
#mySidebar {
   background: #f1f1f1;
}
.parent-tag
{
   position: absolute;
   top: 0;
   left: 0;
}

.parent-tag #mainbtn
{
   background: #fff;
   width: 100% !important;
}
.row.content {height: 1500px}
.sidenav {
   background-color: #f1f1f1;
   height: 100%;
}

.customform
{
width: 350px;
margin-left: 63px;
top: 0;
position: absolute;
padding: 15px;
height: -webkit-fill-available;
overflow: auto;
z-index: 99999;
background: #fff;
height: calc(100% - 43px);
}
::-webkit-scrollbar 
{
   width: 5px;
}
.customform .btn-custom
{
background: #000;
color: #fff;
width: 34px;
padding: 0px 0px;
text-align: center;
right: 0;
position: absolute;
border-radius: 5px;
margin-right: 5px;
}
.customform .title-fr
{
text-align: center;
font-size: 18px;
font-weight: 600;
}
.btn-search input
{
border-radius: 15px;
padding: 0 10px 0px 30px;
height: auto;
}
.btn-search .fa-search
{
position: relative;
left: 25px;
line-height: 1.3;
}
/* Styles cho accordion */
.accordion {
  background-color: #f1f1f1;
  color: #444;
  cursor: pointer;
  padding: 12px;
  width: 100%;
  text-align: left;
  border: none;
  outline: none;
  transition: 0.4s;
  margin-bottom: 2px;
  border-radius: 4px;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.accordion-actions {
  display: flex;
  align-items: center;
}

.accordion-delete {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 8px;
  border-radius: 50%;
  background-color: rgba(255, 0, 0, 0.1);
  transition: background-color 0.3s;
}

.accordion-delete:hover {
  background-color: rgba(255, 0, 0, 0.2);
}

.accordion-delete i {
  color: #ff4d4d;
  font-size: 14px;
}

.accordion:hover {
  background-color: #ddd;
}

.accordion.active {
  background-color: #ccc;
}
.hover-item:hover {
    background-color: #f8f9fa; /* Tương đương với bg-light của Bootstrap */
}
.accordion-title {
  flex: 1;
  cursor: pointer;
  max-width: calc(100% - 55px); /* Để cho phép hiển thị dấu ba chấm (...) khi nội dung quá dài */
  white-space: normal; /* Cho phép xuống dòng */
  word-wrap: break-word; /* Ngắt từ khi cần thiết */
  overflow-wrap: break-word; /* Hỗ trợ tốt hơn cho các trình duyệt mới */
}

.accordion-arrow {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.3s;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
}

.accordion.active .accordion-arrow i {
  transform: rotate(180deg);
}

.panel {
  padding: 0 12px;
  background-color: white;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
  margin-bottom: 10px;
  display: none;
}

.panel.active {
  max-height: 1000px; /* Đủ lớn để chứa nội dung */
  display: block;
  padding: 12px;
  border-bottom: 1px solid #eee;
}
.panel .form-group label
{
display: block;
font-size: 12px;
font-weight: 600;
}
.panel .form-group input, 
.panel .form-group textarea, 
.panel .form-group select
{
width: 100%;
}
.panel .form-group input, 
.panel .form-group textarea, 
.panel .form-group select
{
margin: 8px 0px;
}
.panel .form-group select
{
padding: 3px 0;
}
.panel .form-group input[type="text"], 
.panel .form-group input[type="email"], 
.panel .form-group textarea, 
.panel .form-group select option, 
.panel .form-group select
{
   font-size:12px;
}
.content-form .btn-submit
{
text-align: center;
text-transform: uppercase;
}
.content-form .btn-submit input
{
font-size: 15px;
font-weight: 500;
background: #36d9a6;
padding: 10px 25px;
color: #fff;
border: 1px solid #fff;
border-radius: 10px;
}
.content-form .btn-submit:hover input
{
background: #2ba57e;
}
.btn-tag-close
{
background: #cccccc;
padding: 5px 10px;
color: #333;
font-size: 15px;
}
.btn-tag-close button
{
border: 0;
background: #000;
color: #fff;
padding: 0px 5px;
border-radius: 0;
line-height: 1;
}
.content-sect
{
margin-left: 20px;
}
.main_active
{
background: #a1d0e3;
}
#mainbtn .w3-button:hover
{
background-color: #a1d0e3 !important;
}
#mainbtn .w3-button {
   cursor: pointer;
   width: 100% !important;
   text-align: center;
   padding-top: 10px;
   padding-bottom: 10px;
}
#mainbtn .w3-button i
{
width: 40px;
}
.customform  .sub-title
{
margin-bottom: 1rem;
font-size: 15px;
color: #333;
}
/* On small screens, set height to 'auto' for sidenav and grid */
@media screen and (max-width: 767px) {
.sidenav {
   height: auto;
   padding: 15px;
}
.row.content {height: auto;} 
}

/* Styles cho sidebar và content */
body.sidebar-open {
  overflow-x: hidden;
}

#mySidebar {
  width: 320px;
  position: fixed;
  top: 60px;
  left: 0;
  height: 100%;
  z-index: 99999;
  background-color: white;
  overflow-y: auto;
  transition: transform 0.3s;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

#mySidebar.customform {
  transform: translateX(0);
}

#add-section-btn {
  color: #4DCDB4 !important;
   border-color: #4DCDB4 !important;
}
#add-section-btn:hover {
  background-color: #4DCDB4 !important;
  border-color: #4DCDB4 !important;
  color: #fff !important;
}

/* Responsive styles */
@media (max-width: 768px) {
  #mySidebar {
    width: 280px;
  }
  
  body.sidebar-open {
    margin-left: 280px;
    width: calc(100% - 280px);
  }
}

@media (max-width: 576px) {
  #mySidebar {
    width: 100%;
  }
  
  body.sidebar-open {
    margin-left: 0;
    width: 100%;
  }
}
