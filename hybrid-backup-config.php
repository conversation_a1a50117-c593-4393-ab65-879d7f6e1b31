<?php
/**
 * Hybrid Backup Configuration
 * 
 * This file configures the hybrid backup system that:
 * 1. Triggers API backup creation (non-blocking)
 * 2. Polls local directory every 10 seconds for .wpress file
 * 3. Uploads backup file to API when ready
 */

// Prevent direct access
if (!defined('ABSPATH')) exit;

// Include the hybrid backup solution
require_once plugin_dir_path(__FILE__) . 'hybrid-backup-solution.php';

/**
 * Configuration constants for hybrid backup
 */
define('IPT_HYBRID_BACKUP_ENABLED', true);
define('IPT_HYBRID_BACKUP_POLL_INTERVAL', 10); // seconds
define('IPT_HYBRID_BACKUP_MAX_ATTEMPTS', 30); // 5 minutes of polling
define('IPT_HYBRID_BACKUP_TIMEOUT', 1800); // 30 minutes total timeout

/**
 * Get the appropriate approval action based on configuration
 */
function ipt_get_hybrid_approval_action() {
    if (defined('IPT_HYBRID_BACKUP_ENABLED') && IPT_HYBRID_BACKUP_ENABLED) {
        return 'ipt_handle_hybrid_backup_approval';
    } else {
        return 'ipt_handle_quick_approval'; // Fallback to quick approval
    }
}

/**
 * Enqueue hybrid backup scripts and styles
 */
function enqueue_hybrid_backup_scripts() {
    // Enqueue on frontend pages, especially when approval action is present
    $should_enqueue = !is_admin() && (
        is_page() ||
        is_single() ||
        (isset($_GET['action']) && $_GET['action'] === 'approve') ||
        (isset($_GET['template_id'])) // Additional check for template pages
    );

    if ($should_enqueue) {
        
        // Enqueue JavaScript with cache-busting version
        wp_enqueue_script(
            'hybrid-backup-handler',
            plugin_dir_url(__FILE__) . 'js/hybrid-backup-handler.js',
            array('jquery'),
            '1.0.1-' . time(), // Cache-busting version
            true
        );
        
        // Enqueue CSS with cache-busting version
        wp_enqueue_style(
            'hybrid-backup-styles',
            plugin_dir_url(__FILE__) . 'css/hybrid-backup-styles.css',
            array(),
            '1.0.1-' . time() // Cache-busting version
        );
        
        // Localize script with AJAX URL and nonce
        wp_localize_script('hybrid-backup-handler', 'hybridBackupConfig', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ipt_approval_nonce'),
            'pollInterval' => IPT_HYBRID_BACKUP_POLL_INTERVAL * 1000, // Convert to milliseconds
            'maxAttempts' => IPT_HYBRID_BACKUP_MAX_ATTEMPTS,
            'enabled' => IPT_HYBRID_BACKUP_ENABLED
        ));

        // Also add global ajaxurl for WordPress compatibility
        wp_add_inline_script('hybrid-backup-handler', 'var ajaxurl = "' . admin_url('admin-ajax.php') . '";', 'before');
    }
}
add_action('wp_enqueue_scripts', 'enqueue_hybrid_backup_scripts');

/**
 * Admin interface for hybrid backup configuration
 */
function hybrid_backup_admin_page() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Handle form submission
    if (isset($_POST['update_hybrid_config']) && wp_verify_nonce($_POST['_wpnonce'], 'hybrid_backup_config')) {
        $enabled = isset($_POST['hybrid_backup_enabled']) ? true : false;
        update_option('ipt_hybrid_backup_enabled', $enabled);
        
        $poll_interval = intval($_POST['poll_interval']);
        if ($poll_interval >= 5 && $poll_interval <= 60) {
            update_option('ipt_hybrid_backup_poll_interval', $poll_interval);
        }
        
        $max_attempts = intval($_POST['max_attempts']);
        if ($max_attempts >= 10 && $max_attempts <= 100) {
            update_option('ipt_hybrid_backup_max_attempts', $max_attempts);
        }
        
        echo '<div class="notice notice-success"><p>Hybrid backup configuration updated!</p></div>';
    }
    
    $enabled = get_option('ipt_hybrid_backup_enabled', true);
    $poll_interval = get_option('ipt_hybrid_backup_poll_interval', 10);
    $max_attempts = get_option('ipt_hybrid_backup_max_attempts', 30);
    
    ?>
    <div class="wrap">
        <h1>🔄 Hybrid Backup Configuration</h1>
        
        <div style="background: #fff; padding: 20px; border: 1px solid #ccd0d4; border-radius: 4px; margin: 20px 0;">
            <h2>📋 How Hybrid Backup Works</h2>
            <ol>
                <li><strong>🚀 Trigger API Backup:</strong> Call external API to create backup (non-blocking)</li>
                <li><strong>🔍 Poll Local Directory:</strong> Check every 10 seconds for new .wpress file</li>
                <li><strong>📤 Upload to API:</strong> When backup file is found, upload to API server</li>
                <li><strong>✅ Complete Approval:</strong> Use returned code_file_id for approval process</li>
            </ol>
            <p><em>This approach avoids 524 timeout errors while still creating and uploading backup files.</em></p>
        </div>
        
        <form method="post" action="">
            <?php wp_nonce_field('hybrid_backup_config'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">Enable Hybrid Backup</th>
                    <td>
                        <label>
                            <input type="checkbox" name="hybrid_backup_enabled" value="1" <?php checked($enabled); ?>>
                            Enable hybrid backup system
                        </label>
                        <p class="description">
                            When enabled, approval process will create and upload backup files.<br>
                            When disabled, uses quick approval (no backup).
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">Poll Interval</th>
                    <td>
                        <input type="number" name="poll_interval" value="<?php echo esc_attr($poll_interval); ?>" min="5" max="60" step="1">
                        <span>seconds</span>
                        <p class="description">
                            How often to check for new backup files (5-60 seconds).
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">Max Polling Attempts</th>
                    <td>
                        <input type="number" name="max_attempts" value="<?php echo esc_attr($max_attempts); ?>" min="10" max="100" step="1">
                        <span>attempts</span>
                        <p class="description">
                            Maximum number of polling attempts before timeout (10-100).<br>
                            Total timeout = Poll Interval × Max Attempts
                        </p>
                    </td>
                </tr>
            </table>
            
            <div style="background: #f0f6fc; padding: 15px; border-left: 4px solid #0073aa; margin: 20px 0;">
                <h3>📊 Current Configuration</h3>
                <ul>
                    <li><strong>Status:</strong> <?php echo $enabled ? '✅ Enabled' : '❌ Disabled'; ?></li>
                    <li><strong>Poll Interval:</strong> <?php echo $poll_interval; ?> seconds</li>
                    <li><strong>Max Attempts:</strong> <?php echo $max_attempts; ?> attempts</li>
                    <li><strong>Total Timeout:</strong> <?php echo ($poll_interval * $max_attempts / 60); ?> minutes</li>
                    <li><strong>Action Used:</strong> <code><?php echo ipt_get_hybrid_approval_action(); ?></code></li>
                </ul>
            </div>
            
            <?php submit_button('Update Configuration', 'primary', 'update_hybrid_config'); ?>
        </form>
        
        <div style="background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0;">
            <h3>⚠️ Important Notes</h3>
            <ul>
                <li><strong>Backup Directory:</strong> <code>/wp-content/ai1wm-backups/</code></li>
                <li><strong>File Format:</strong> <code>.wpress</code> files</li>
                <li><strong>API Endpoint:</strong> <code>https://ipt-wp-api.weaveform.com/wpcli</code></li>
                <li><strong>Upload Endpoint:</strong> Configured in <code>BACKUP_UPLOAD_API_URL</code></li>
                <li><strong>Dependencies:</strong> All-in-One WP Migration plugin must be installed</li>
            </ul>
        </div>
        
        <div style="background: #d1ecf1; padding: 15px; border-left: 4px solid #bee5eb; margin: 20px 0;">
            <h3>🧪 Testing</h3>
            <p>To test the hybrid backup system:</p>
            <ol>
                <li>Enable hybrid backup above</li>
                <li>Go to a page with approval functionality</li>
                <li>Click "Approve" on a template</li>
                <li>Watch the progress UI show the 3-step process</li>
                <li>Check browser console for detailed logs</li>
            </ol>
            <p><strong>Expected behavior:</strong> Process completes in 5-15 minutes without 524 errors.</p>
        </div>
    </div>
    <?php
}

/**
 * Add admin menu for hybrid backup configuration
 */
function hybrid_backup_admin_menu() {
    add_options_page(
        'Hybrid Backup Configuration',
        'Hybrid Backup',
        'manage_options',
        'hybrid-backup-config',
        'hybrid_backup_admin_page'
    );
}
add_action('admin_menu', 'hybrid_backup_admin_menu');

/**
 * Add admin notice about hybrid backup
 */
function hybrid_backup_admin_notice() {
    if (current_user_can('manage_options')) {
        $enabled = get_option('ipt_hybrid_backup_enabled', true);
        $status = $enabled ? 'Enabled' : 'Disabled';
        $color = $enabled ? 'notice-info' : 'notice-warning';
        
        echo '<div class="notice ' . $color . ' is-dismissible">';
        echo '<p><strong>Hybrid Backup System:</strong> ' . $status . ' ';
        echo '| <a href="' . admin_url('options-general.php?page=hybrid-backup-config') . '">Configure</a></p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'hybrid_backup_admin_notice');

/**
 * JavaScript integration for approval buttons
 */
function hybrid_backup_approval_script() {
    if (!is_admin() && (is_page() || is_single())) {
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Find approval buttons and attach hybrid backup handler
            const approvalButtons = document.querySelectorAll('[data-action="approve"]');
            
            approvalButtons.forEach(function(button) {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Check if hybrid backup is enabled
                    if (typeof hybridBackupConfig !== 'undefined' && hybridBackupConfig.enabled) {
                        console.log('🔄 Using hybrid backup system');
                        
                        // Get form data
                        const form = button.closest('form') || document.querySelector('#approval-form');
                        if (form) {
                            const formData = new FormData(form);
                            formData.set('action', '<?php echo ipt_get_hybrid_approval_action(); ?>');
                            formData.set('security', hybridBackupConfig.nonce);
                            
                            // Start hybrid backup process
                            if (window.hybridBackupHandler) {
                                window.hybridBackupHandler.startProcess(formData);
                            } else {
                                console.error('Hybrid backup handler not initialized');
                            }
                        }
                    } else {
                        console.log('⚡ Using quick approval (hybrid backup disabled)');
                        // Fall back to quick approval
                        // Your existing approval logic here
                    }
                });
            });
        });
        </script>
        <?php
    }
}
add_action('wp_footer', 'hybrid_backup_approval_script');
?>
